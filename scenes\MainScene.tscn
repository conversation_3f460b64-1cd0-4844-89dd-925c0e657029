[gd_scene load_steps=27 format=3 uid="uid://bveyv12m1ktvb"]

[ext_resource type="Texture2D" uid="uid://4pw36hbqxi3h" path="res://assets/LifeSupportMonitor.PNG" id="1_ein5b"]
[ext_resource type="Script" uid="uid://cy3gxhwasdu8f" path="res://scenes/main_scene.gd" id="1_filsn"]
[ext_resource type="Script" uid="uid://lhm6w3qnoej4" path="res://scenes/simulation/simulation.gd" id="1_kligj"]
[ext_resource type="Texture2D" uid="uid://cv5xgha0ew7al" path="res://assets/redlight.PNG" id="2_t0kbg"]
[ext_resource type="Texture2D" uid="uid://dxgqldy66ocfn" path="res://scenes/simulation/alien_ship.png" id="3_08o8g"]
[ext_resource type="Script" uid="uid://duxjspuqyqymy" path="res://scenes/area_scan/area_scan.gd" id="3_hqhgl"]
[ext_resource type="Texture2D" uid="uid://c7tdxi2ow4017" path="res://assets/telegraph.PNG" id="3_maaih"]
[ext_resource type="Script" uid="uid://c2rfnr0nxyb6v" path="res://scenes/health/health.gd" id="4_1wiy7"]
[ext_resource type="Script" uid="uid://cu7m518w3wfjk" path="res://scenes/cargo/cargo.gd" id="4_kp0ql"]
[ext_resource type="Script" uid="uid://wxdwpl366s6u" path="res://scenes/population/population.gd" id="6_o3jky"]
[ext_resource type="FontFile" uid="uid://c58ewoppxugc" path="res://assets/fonts/Solari.ttf" id="7_kp0ql"]
[ext_resource type="Script" uid="uid://86xokvl55eyg" path="res://scenes/ship/ship.gd" id="7_u1u68"]
[ext_resource type="Script" uid="uid://0iesb31bbfoy" path="res://scenes/computer_screen/computer_screen.gd" id="9_cfq0f"]
[ext_resource type="Script" uid="uid://j20lgil7inx4" path="res://scenes/debug.gd" id="9_x537r"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_yidh4"]
bg_color = Color(0.0846899, 0.0846899, 0.0846899, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_si70g"]
bg_color = Color(0.0862745, 0.0862745, 0.0862745, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
corner_radius_top_left = 3
corner_radius_top_right = 3
corner_radius_bottom_right = 3
corner_radius_bottom_left = 3

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xqefg"]
bg_color = Color(0.197937, 0.197937, 0.197937, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_08o8g"]
bg_color = Color(1, 1, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0cxu4"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xnxdc"]
bg_color = Color(0.74902, 0.121569, 0.211765, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0ehpd"]
bg_color = Color(0.74902, 0.498039, 0.211765, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8wjpf"]
bg_color = Color(0.23887, 0.507819, 0.396601, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_25dfr"]
bg_color = Color(0.330224, 0.541744, 0.967697, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_arfit"]
bg_color = Color(0.0862745, 0.0862745, 0.0862745, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_eldo8"]
bg_color = Color(1, 1, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_s4fry"]
bg_color = Color(1, 1, 1, 1)

[node name="Control" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_filsn")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="notificationVBox" type="VBoxContainer" parent="CanvasLayer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(300, 0)
anchors_preset = 11
anchor_left = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 0
grow_vertical = 2
alignment = 2

[node name="BackgroundPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_yidh4")

[node name="Simulation" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -634.0
offset_top = -405.0
offset_right = -358.0
offset_bottom = 11.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_kligj")

[node name="Panel" type="Panel" parent="Simulation"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="timeLabel" type="Label" parent="Simulation/Panel"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 75.0
offset_bottom = 36.0
text = "12:24"

[node name="ampmLabel" type="Label" parent="Simulation/Panel"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 77.0
offset_top = 16.0
offset_right = 118.0
offset_bottom = 39.0
theme_override_font_sizes/font_size = 8
text = "PM"

[node name="LocationOptions" type="VBoxContainer" parent="Simulation/Panel"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 200)
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 0

[node name="AlienShip" type="Sprite2D" parent="Simulation"]
position = Vector2(136, 122)
scale = Vector2(0.503125, 0.503125)
texture = ExtResource("3_08o8g")

[node name="AreaScan" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -323.0
offset_top = -405.0
offset_right = -47.0
offset_bottom = 11.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("3_hqhgl")

[node name="VBoxContainer" type="VBoxContainer" parent="AreaScan"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="ScanButton" type="Button" parent="AreaScan/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/normal = SubResource("StyleBoxFlat_xqefg")
text = "SCAN AREA"

[node name="VSplitContainer" type="VSplitContainer" parent="AreaScan/VBoxContainer"]
custom_minimum_size = Vector2(0, 15.64)
layout_mode = 2

[node name="ScannerUI" type="Panel" parent="AreaScan/VBoxContainer"]
unique_name_in_owner = true
clip_contents = true
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="Cargo" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -13.0
offset_top = -405.0
offset_right = 263.0
offset_bottom = 11.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("4_kp0ql")

[node name="Panel" type="Panel" parent="Cargo"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
offset_right = 2.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="Label" type="Label" parent="Cargo/Panel"]
layout_mode = 0
offset_left = 111.0
offset_top = 11.0
offset_right = 167.0
offset_bottom = 34.0
text = "CARGO"

[node name="PeopleCountLabel" type="Label" parent="Cargo/Panel"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 111.0
offset_top = 171.0
offset_right = 167.0
offset_bottom = 194.0
text = "[2 / 6]"
horizontal_alignment = 1

[node name="PeoplePanel" type="Panel" parent="Cargo/Panel"]
unique_name_in_owner = true
clip_contents = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 71.0
offset_top = 62.0
offset_right = -67.0
offset_bottom = -263.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="CargoCapacityBar" type="ProgressBar" parent="Cargo/Panel"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 26.0
offset_top = 162.0
offset_right = 251.0
offset_bottom = 171.0
theme_override_styles/fill = SubResource("StyleBoxFlat_08o8g")
value = 39.55
show_percentage = false

[node name="EATBar" type="ProgressBar" parent="Cargo/Panel/CargoCapacityBar"]
unique_name_in_owner = true
layout_mode = 0
offset_right = 225.0
offset_bottom = 9.0
theme_override_styles/background = SubResource("StyleBoxFlat_0cxu4")
theme_override_styles/fill = SubResource("StyleBoxFlat_xnxdc")
value = 29.84
show_percentage = false

[node name="FUELBar" type="ProgressBar" parent="Cargo/Panel/CargoCapacityBar"]
unique_name_in_owner = true
layout_mode = 0
offset_right = 225.0
offset_bottom = 9.0
theme_override_styles/background = SubResource("StyleBoxFlat_0cxu4")
theme_override_styles/fill = SubResource("StyleBoxFlat_0ehpd")
value = 21.52
show_percentage = false

[node name="DISECTBar" type="ProgressBar" parent="Cargo/Panel/CargoCapacityBar"]
unique_name_in_owner = true
layout_mode = 0
offset_right = 225.0
offset_bottom = 9.0
theme_override_styles/background = SubResource("StyleBoxFlat_0cxu4")
theme_override_styles/fill = SubResource("StyleBoxFlat_8wjpf")
value = 14.54
show_percentage = false

[node name="PROBEBar" type="ProgressBar" parent="Cargo/Panel/CargoCapacityBar"]
unique_name_in_owner = true
layout_mode = 0
offset_right = 225.0
offset_bottom = 9.0
theme_override_styles/background = SubResource("StyleBoxFlat_0cxu4")
theme_override_styles/fill = SubResource("StyleBoxFlat_25dfr")
value = 11.54
show_percentage = false

[node name="CargoProcessingBar" type="ProgressBar" parent="Cargo/Panel"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 93.0
offset_top = 48.0
offset_right = 184.0
offset_bottom = 57.0
value = 29.92
show_percentage = false

[node name="HBoxContainer" type="HBoxContainer" parent="Cargo/Panel"]
layout_mode = 0
offset_left = 10.0
offset_top = 293.0
offset_right = 264.0
offset_bottom = 380.0

[node name="EatButton" type="Button" parent="Cargo/Panel/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "EAT"

[node name="FuelButton" type="Button" parent="Cargo/Panel/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "FUEL"

[node name="DisectButton" type="Button" parent="Cargo/Panel/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "DISECT"

[node name="ProbeButton" type="Button" parent="Cargo/Panel/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "PROBE"

[node name="Research" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 303.0
offset_top = -405.0
offset_right = 579.0
offset_bottom = 11.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="Research"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="Label" type="Label" parent="Research/Panel"]
layout_mode = 0
offset_left = 67.0
offset_top = 11.0
offset_right = 214.0
offset_bottom = 34.0
text = "INSERT CARTRIDGE"

[node name="Health" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -632.0
offset_top = 49.0
offset_right = -356.0
offset_bottom = 204.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("4_1wiy7")

[node name="Panel" type="Panel" parent="Health"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="MarginContainer" type="MarginContainer" parent="Health/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 5
theme_override_constants/margin_top = 5
theme_override_constants/margin_right = 5
theme_override_constants/margin_bottom = 5

[node name="VBoxContainer" type="VBoxContainer" parent="Health/Panel/MarginContainer"]
layout_mode = 2

[node name="TextureRect" type="TextureRect" parent="Health/Panel/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 80)
layout_mode = 2
texture = ExtResource("1_ein5b")
expand_mode = 1

[node name="Label" type="Label" parent="Health/Panel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "FOOD"
horizontal_alignment = 1

[node name="FoodBar" type="ProgressBar" parent="Health/Panel/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 15)
layout_mode = 2
theme_override_styles/background = SubResource("StyleBoxFlat_arfit")
theme_override_styles/fill = SubResource("StyleBoxFlat_eldo8")
value = 33.64
show_percentage = false

[node name="FoodLabel" type="Label" parent="Health/Panel/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "[89.2 / 100]"
horizontal_alignment = 2

[node name="Population" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -632.0
offset_top = 229.0
offset_right = -356.0
offset_bottom = 384.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("6_o3jky")

[node name="Panel" type="Panel" parent="Population"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="Label" type="Label" parent="Population/Panel"]
layout_mode = 0
offset_left = 7.0
offset_top = 12.0
offset_right = 176.0
offset_bottom = 35.0
text = "HUMAN POPULATION"

[node name="humanpopContainer" type="Control" parent="Population/Panel"]
anchors_preset = 0
offset_left = 8.0
offset_top = 37.0
offset_right = 268.0
offset_bottom = 77.0

[node name="HBoxContainer" type="HBoxContainer" parent="Population/Panel/humanpopContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="10" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="HSplitContainer3" type="HSplitContainer" parent="Population/Panel/humanpopContainer/HBoxContainer"]
layout_mode = 2

[node name="9" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="8" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="7" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="HSplitContainer2" type="HSplitContainer" parent="Population/Panel/humanpopContainer/HBoxContainer"]
layout_mode = 2

[node name="6" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="5" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="4" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="HSplitContainer" type="HSplitContainer" parent="Population/Panel/humanpopContainer/HBoxContainer"]
layout_mode = 2

[node name="3" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="2" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="1" type="Label" parent="Population/Panel/humanpopContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="Label2" type="Label" parent="Population/Panel"]
layout_mode = 0
offset_left = 7.0
offset_top = 80.0
offset_right = 176.0
offset_bottom = 103.0
text = "FOLLOWERS"

[node name="followerContainer" type="Control" parent="Population/Panel"]
anchors_preset = 0
offset_left = 8.0
offset_top = 104.0
offset_right = 268.0
offset_bottom = 144.0

[node name="HBoxContainer" type="HBoxContainer" parent="Population/Panel/followerContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="10f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="HSplitContainer3" type="HSplitContainer" parent="Population/Panel/followerContainer/HBoxContainer"]
layout_mode = 2

[node name="9f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="8f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="7f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="HSplitContainer2" type="HSplitContainer" parent="Population/Panel/followerContainer/HBoxContainer"]
layout_mode = 2

[node name="6f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="5f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="4f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="HSplitContainer" type="HSplitContainer" parent="Population/Panel/followerContainer/HBoxContainer"]
layout_mode = 2

[node name="3f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="2f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="1f" type="Label" parent="Population/Panel/followerContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_fonts/font = ExtResource("7_kp0ql")
theme_override_font_sizes/font_size = 32
text = "8"
horizontal_alignment = 1

[node name="Threat" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -331.0
offset_top = 49.0
offset_right = -258.0
offset_bottom = 384.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="Threat"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="MarginContainer" type="MarginContainer" parent="Threat"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Threat/MarginContainer"]
layout_mode = 2

[node name="TextureRect" type="TextureRect" parent="Threat/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 80)
layout_mode = 2
texture = ExtResource("2_t0kbg")
expand_mode = 3

[node name="Control" type="Control" parent="Threat/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="Label" type="Label" parent="Threat/MarginContainer/VBoxContainer/Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -6.5
offset_top = -76.5
offset_right = 6.5
offset_bottom = 76.5
grow_horizontal = 2
grow_vertical = 2
text = "T
H
R
E
A
T"
horizontal_alignment = 1

[node name="ThreatBar" type="ProgressBar" parent="Threat/MarginContainer/VBoxContainer/Control"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_vertical = 3
theme_override_styles/fill = SubResource("StyleBoxFlat_s4fry")
value = 35.39
fill_mode = 3
show_percentage = false

[node name="Label2" type="Label" parent="Threat/MarginContainer/VBoxContainer/Control/ThreatBar"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -6.5
offset_top = -76.5
offset_right = 6.5
offset_bottom = 76.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
text = "T
H
R
E
A
T"
horizontal_alignment = 1
clip_text = true

[node name="Ship" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -228.0
offset_top = 49.0
offset_right = 33.0
offset_bottom = 384.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("7_u1u68")

[node name="Panel" type="Panel" parent="Ship"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="MarginContainer" type="MarginContainer" parent="Ship/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 5
theme_override_constants/margin_top = 5
theme_override_constants/margin_right = 5
theme_override_constants/margin_bottom = 5

[node name="VBoxContainer" type="VBoxContainer" parent="Ship/Panel/MarginContainer"]
layout_mode = 2

[node name="TextureRect" type="TextureRect" parent="Ship/Panel/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 150)
layout_mode = 2
texture = ExtResource("3_maaih")
expand_mode = 3

[node name="Label" type="Label" parent="Ship/Panel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "FUEL CONSUMPTION"
horizontal_alignment = 1

[node name="FuelBar" type="ProgressBar" parent="Ship/Panel/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 15)
layout_mode = 2
theme_override_styles/background = SubResource("StyleBoxFlat_arfit")
theme_override_styles/fill = SubResource("StyleBoxFlat_eldo8")
value = 33.64

[node name="FuelAmountLabel" type="Label" parent="Ship/Panel/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "[812 / 1000]"
horizontal_alignment = 2

[node name="DurabilityBar" type="ProgressBar" parent="Ship/Panel/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 15)
layout_mode = 2
theme_override_styles/background = SubResource("StyleBoxFlat_arfit")
theme_override_styles/fill = SubResource("StyleBoxFlat_eldo8")
value = 69.71
show_percentage = false

[node name="SpeedBar" type="ProgressBar" parent="Ship/Panel/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 15)
layout_mode = 2
theme_override_styles/background = SubResource("StyleBoxFlat_arfit")
theme_override_styles/fill = SubResource("StyleBoxFlat_eldo8")
value = 22.04
show_percentage = false

[node name="ComputerScreen" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 52.0
offset_top = 52.0
offset_right = 578.0
offset_bottom = 387.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("9_cfq0f")

[node name="StreamingTextPanel" type="Panel" parent="ComputerScreen"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_si70g")

[node name="Debug" type="Control" parent="."]
anchors_preset = 0
offset_right = 143.0
offset_bottom = 76.0
script = ExtResource("9_x537r")

[node name="Button" type="Button" parent="Debug"]
layout_mode = 0
offset_left = 32.0
offset_top = 21.0
offset_right = 90.0
offset_bottom = 52.0
text = "Debug"

[node name="DebugPanel" type="Panel" parent="Debug"]
visible = false
layout_mode = 0
offset_left = 42.0
offset_top = 97.0
offset_right = 845.0
offset_bottom = 603.0

[node name="debugGrid" type="GridContainer" parent="Debug/DebugPanel"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
