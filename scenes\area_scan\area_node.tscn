[gd_scene load_steps=6 format=3 uid="uid://dv1ky4aankfkl"]

[ext_resource type="Texture2D" uid="uid://bax5d4c4tgdi1" path="res://assets/icons/wood.png" id="1_wa5mo"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uxs2s"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_spyi3"]
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2aj08"]
bg_color = Color(0.851769, 0.851769, 0.851769, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1hiyk"]
bg_color = Color(0.85098, 0.85098, 0.85098, 1)
corner_radius_top_left = 2
corner_radius_top_right = 2
corner_radius_bottom_right = 2
corner_radius_bottom_left = 2

[node name="AreaNode" type="Control"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 3
anchors_preset = 0
offset_right = 50.0
offset_bottom = 50.0

[node name="Button" type="Button" parent="."]
unique_name_in_owner = true
z_index = 1
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 0.5
offset_bottom = -11.5
grow_horizontal = 2
grow_vertical = 2
focus_mode = 0
theme_override_styles/hover = SubResource("StyleBoxFlat_uxs2s")
theme_override_styles/pressed = SubResource("StyleBoxFlat_spyi3")
theme_override_styles/normal = SubResource("StyleBoxFlat_2aj08")

[node name="Panel" type="Panel" parent="Button"]
z_index = -1
layout_mode = 0
offset_left = 25.0
offset_top = 26.5
offset_right = 39.0
offset_bottom = 40.5
rotation = 0.785398
theme_override_styles/panel = SubResource("StyleBoxFlat_1hiyk")

[node name="Marker2D" type="Marker2D" parent="Button/Panel"]
position = Vector2(12.7279, 12.7279)
rotation = -0.785398

[node name="Icon" type="TextureRect" parent="Button"]
unique_name_in_owner = true
modulate = Color(0, 0, 0, 1)
custom_minimum_size = Vector2(30, 30)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -15.0
offset_top = -15.0
offset_right = 15.0
offset_bottom = 15.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("1_wa5mo")
expand_mode = 1
stretch_mode = 5
