extends Node

# ============================================================================
# SIGNAL BUS - Central hub for all game signals
# ============================================================================
# This autoload acts as the central communication hub for all game systems.
# Instead of connecting directly between systems, everything goes through here.
#
# ADDING NEW SIGNALS:
# 1. Declare the signal below in the appropriate category
# 2. Add an emit function if needed for external systems to trigger it
# 3. Connect to it from any scene that needs to react
#
# EXAMPLE - Adding a new signal:
# signal player_health_changed(new_health: int, max_health: int)
#
# func emit_player_health_changed(new_health: int, max_health: int):
#     player_health_changed.emit(new_health, max_health)
#
# EXAMPLE - Connecting to signals in a scene:
# func _ready():
#     SignalBus.time_changed.connect(_on_time_changed)
#     SignalBus.day_changed.connect(_on_day_changed)
#     SignalBus.game_saved.connect(_on_game_saved)
#
# func _on_time_changed(hours: int, minutes: int):
#     # Update UI clock display
#     clock_label.text = "%02d:%02d" % [hours, minutes]
#
# func _on_day_changed(new_day: int):
#     # Handle new day events
#     day_label.text = "Day " + str(new_day)
#     # Maybe trigger daily events, reset counters, etc.
#
# func _on_game_saved():
#     # Show save confirmation UI
#     show_notification("Game Saved!")
#
# EXAMPLE - Emitting signals from other systems:
# SignalBus.emit_game_saved()
# SignalBus.emit_menu_opened()
# SignalBus.emit_player_health_changed(75, 100)
# ============================================================================

# ============================================================================
# SIGNAL DECLARATIONS
# ============================================================================

# Time & Game State Signals
signal time_changed(hours: int, minutes: int)
signal day_changed(new_day: int)
signal game_paused()
signal game_resumed()

# Save/Load Signals
signal game_saved()
signal game_loaded()
signal save_failed(error_message: String)
signal load_failed(error_message: String)

# UI Signals
signal menu_opened(menu_name: String)
signal menu_closed(menu_name: String)
signal notification_requested(message: String, duration: float)

# Player Stats Signals
signal player_health_changed(current_health: float, max_health: float)
signal player_food_changed(current_food: float, max_food: float)
signal player_fuel_changed(current_fuel: float, max_fuel: float)
signal player_currency_changed(currency: int)
signal player_research_changed(research_points: int)

# Scan System Signals
signal player_scan_triggered(scan_position: Vector2)
signal scan_cooldown_started(cooldown_duration: float)
signal scan_cooldown_finished()

# Alien Invasion Game Mechanics Signals
signal human_population_changed(new_population: int)
signal followers_changed(new_followers: int)
signal victory_condition_met(population: int, followers: int)
signal ship_human_capacity_changed(new_capacity: int)

# Location Interaction Signals
signal location_arrived(resource_type: int, resource_node: Control)
signal location_option_selected(option_data: Dictionary)
signal location_options_cleared()

# Future expansion examples (commented out):
# signal inventory_updated(item_name: String, quantity: int)
# signal research_completed(research_name: String)
# signal threat_level_changed(new_level: int)

# ============================================================================
# EMIT FUNCTIONS - For other systems to trigger signals
# ============================================================================

# Time & Game State
func emit_time_changed(hours: int, minutes: int):
	time_changed.emit(hours, minutes)

func emit_day_changed(new_day: int):
	day_changed.emit(new_day)

func emit_game_paused():
	game_paused.emit()

func emit_game_resumed():
	game_resumed.emit()

# Save/Load
func emit_game_saved():
	game_saved.emit()
	print("Game saved - SignalBus")

func emit_game_loaded():
	game_loaded.emit()

func emit_save_failed(error_message: String):
	save_failed.emit(error_message)

func emit_load_failed(error_message: String):
	load_failed.emit(error_message)

# UI
func emit_menu_opened(menu_name: String):
	menu_opened.emit(menu_name)

func emit_menu_closed(menu_name: String):
	menu_closed.emit(menu_name)

func emit_notification_requested(message: String, duration: float = 3.0):
	notification_requested.emit(message, duration)

# Player Stats
func emit_player_health_changed(current_health: float, max_health: float):
	player_health_changed.emit(current_health, max_health)

func emit_player_food_changed(current_food: float, max_food: float):
	player_food_changed.emit(current_food, max_food)

func emit_player_fuel_changed(current_fuel: float, max_fuel: float):
	player_fuel_changed.emit(current_fuel, max_fuel)

func emit_player_currency_changed(currency: int):
	player_currency_changed.emit(currency)

func emit_player_research_changed(research_points: int):
	player_research_changed.emit(research_points)

# Scan System
func emit_player_scan_triggered(scan_position: Vector2):
	player_scan_triggered.emit(scan_position)

func emit_scan_cooldown_started(cooldown_duration: float):
	scan_cooldown_started.emit(cooldown_duration)

func emit_scan_cooldown_finished():
	scan_cooldown_finished.emit()

# Alien Invasion Game Mechanics
func emit_human_population_changed(new_population: int):
	human_population_changed.emit(new_population)

func emit_followers_changed(new_followers: int):
	followers_changed.emit(new_followers)

func emit_victory_condition_met(population: int, followers: int):
	victory_condition_met.emit(population, followers)

func emit_ship_human_capacity_changed(new_capacity: int):
	ship_human_capacity_changed.emit(new_capacity)

# Location Interaction
func emit_location_arrived(resource_type: int, resource_node: Control):
	location_arrived.emit(resource_type, resource_node)

func emit_location_option_selected(option_data: Dictionary):
	location_option_selected.emit(option_data)

func emit_location_options_cleared():
	location_options_cleared.emit()
