extends Control

# Resource types that can be found
enum ResourceType {
	FUEL,
	FOOD,
	PE<PERSON>LE,
	CROPCI<PERSON>LE
}

# Node references
@onready var button: Button = %Button
@onready var icon: TextureRect = %Icon

# Resource properties
var resource_type: ResourceType
var resource_data: Dictionary = {}

# Lifecycle management
var lifetime_timer: Timer
var fade_timer: Timer
var is_fading: bool = false

# Selection state
var is_selected: bool = false
var selected_button_color: Color = Color(0.2, 0.8, 0.3, 1.0)  # Alien greenish
var normal_button_color: Color = Color(0.85098, 0.85098, 0.85098, 1)  # Original button color
var original_style_box: StyleBox

# Settings
var total_lifetime: float = 30.0  # Total time before node disappears
var fade_duration: float = 5.0    # Time spent fading out
var fade_start_time: float = 25.0  # When to start fading (25 seconds)

# Resource type definitions
var resource_definitions = {
	ResourceType.FUEL: {
		"name": "Fuel Depot",
		"icon_path": "res://assets/icons/wood.png",  # Placeholder for now
		"color": Color.BLACK
	},
	ResourceType.FOOD: {
		"name": "Food Cache",
		"icon_path": "res://assets/icons/food.png",  # Placeholder for now
		"color": Color.BLACK
	},
	ResourceType.PEOPLE: {
		"name": "Survivors",
		"icon_path": "res://assets/icons/person.png",  # Placeholder for now
		"color": Color.BLACK
	},
	ResourceType.CROPCIRCLE: {
		"name": "Survivors",
		"icon_path": "res://assets/icons/wheat.png",  # Placeholder for now
		"color": Color.BLACK
	}
}

func _ready():
	setup_timers()
	setup_button()

func setup_timers():
	# Create lifetime timer (starts fade at 25 seconds)
	lifetime_timer = Timer.new()
	lifetime_timer.wait_time = fade_start_time
	lifetime_timer.one_shot = true
	lifetime_timer.timeout.connect(_on_lifetime_timer_timeout)
	add_child(lifetime_timer)

	# Create fade timer
	fade_timer = Timer.new()
	fade_timer.wait_time = fade_duration
	fade_timer.one_shot = true
	fade_timer.timeout.connect(_on_fade_timer_timeout)
	add_child(fade_timer)

	# Start the lifetime timer
	lifetime_timer.start()

func setup_button():
	# Connect button press to resource interaction
	button.pressed.connect(_on_button_pressed)

func initialize_resource(type: ResourceType, data: Dictionary = {}):
	## Initialize this node with a specific resource type and data
	resource_type = type
	resource_data = data

	# Get resource definition
	var definition = resource_definitions[resource_type]

	# Load and set the icon
	var texture = load(definition.icon_path)
	if texture:
		icon.texture = texture

	# Keep icon color black (don't change it)
	icon.modulate = Color(0, 0, 0, 1)  # Always black

	# Store the original button style for later restoration
	original_style_box = button.get_theme_stylebox("normal")

	# Store resource name for potential tooltip/UI
	tooltip_text = definition.name

func _on_lifetime_timer_timeout():
	## Start fading when lifetime expires (only if not selected)
	if not is_selected and not is_fading:
		start_fade()

func start_fade():
	## Begin the fade out process
	is_fading = true
	fade_timer.start()
	
	# Create fade tween
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, fade_duration)

func _on_fade_timer_timeout():
	## Remove the node when fade is complete
	queue_free()

func _on_button_pressed():
	print("resource button pressed")
	## Handle resource selection when button is pressed
	# Navigate up the hierarchy: AreaNode -> ScannerUI -> VBoxContainer -> AreaScan
	var scanner_ui = get_parent()  # ScannerUI
	var vbox_container = scanner_ui.get_parent()  # VBoxContainer
	var area_scan = vbox_container.get_parent()  # AreaScan

	print("Scanner UI: ", scanner_ui.name if scanner_ui else "null")
	print("VBox Container: ", vbox_container.name if vbox_container else "null")
	print("Area Scan: ", area_scan.name if area_scan else "null")

	if area_scan and area_scan.has_method("_on_resource_node_selected"):
		print("parent found - calling selection method")
		area_scan._on_resource_node_selected(self)
	else:
		print("parent method not found")

func set_selected(selected: bool):
	## Set the selection state of this node
	is_selected = selected

	if is_selected:
		# Change button background to green and stop fade timers
		var green_style = StyleBoxFlat.new()
		green_style.bg_color = selected_button_color
		green_style.corner_radius_top_left = 4
		green_style.corner_radius_top_right = 4
		green_style.corner_radius_bottom_right = 4
		green_style.corner_radius_bottom_left = 4
		button.add_theme_stylebox_override("normal", green_style)

		if lifetime_timer:
			lifetime_timer.stop()
		if fade_timer:
			fade_timer.stop()
		is_fading = false
		modulate.a = 1.0  # Ensure fully visible
	else:
		# Restore original button style and restart fade timer
		if original_style_box:
			button.add_theme_stylebox_override("normal", original_style_box)
		restart_fade_timer()

func restart_fade_timer():
	## Restart the fade timer for unselected nodes
	if not is_selected and lifetime_timer:
		lifetime_timer.wait_time = fade_start_time
		lifetime_timer.start()

func force_cleanup():
	## Force immediate cleanup of this node (called when new scan happens)
	if lifetime_timer:
		lifetime_timer.stop()
	if fade_timer:
		fade_timer.stop()
	queue_free()
