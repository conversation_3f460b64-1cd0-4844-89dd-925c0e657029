extends Control

# Resource types that can be found
enum ResourceType {
	FUE<PERSON>,
	FOOD,
	PE<PERSON>LE,
	CROP<PERSON><PERSON><PERSON>
}

# Node references
@onready var button: Button = %Button
@onready var icon: TextureRect = %Icon

# Resource properties
var resource_type: ResourceType
var resource_data: Dictionary = {}

# Lifecycle management
var lifetime_timer: Timer
var fade_timer: Timer
var is_fading: bool = false

# Settings
var total_lifetime: float = 30.0  # Total time before node disappears
var fade_duration: float = 5.0    # Time spent fading out

# Resource type definitions
var resource_definitions = {
	ResourceType.FUEL: {
		"name": "Fuel Depot",
		"icon_path": "res://assets/icons/wood.png",  # Placeholder for now
		"color": Color.BLACK
	},
	ResourceType.FOOD: {
		"name": "Food Cache",
		"icon_path": "res://assets/icons/food.png",  # Placeholder for now
		"color": Color.BLACK
	},
	ResourceType.PEOPLE: {
		"name": "Survivors",
		"icon_path": "res://assets/icons/people.png",  # Placeholder for now
		"color": Color.BLACK
	},
	ResourceType.CROPCIRCLE: {
		"name": "Survivors",
		"icon_path": "res://assets/icons/wheat.png",  # Placeholder for now
		"color": Color.BLACK
	}
}

func _ready():
	setup_timers()
	setup_button()

func setup_timers():
	# Create lifetime timer
	lifetime_timer = Timer.new()
	lifetime_timer.wait_time = total_lifetime - fade_duration
	lifetime_timer.one_shot = true
	lifetime_timer.timeout.connect(_on_lifetime_timer_timeout)
	add_child(lifetime_timer)
	
	# Create fade timer
	fade_timer = Timer.new()
	fade_timer.wait_time = fade_duration
	fade_timer.one_shot = true
	fade_timer.timeout.connect(_on_fade_timer_timeout)
	add_child(fade_timer)
	
	# Start the lifetime timer
	lifetime_timer.start()

func setup_button():
	# Connect button press to resource interaction
	button.pressed.connect(_on_button_pressed)

func initialize_resource(type: ResourceType, data: Dictionary = {}):
	"""Initialize this node with a specific resource type and data"""
	resource_type = type
	resource_data = data
	
	# Get resource definition
	var definition = resource_definitions[resource_type]
	
	# Load and set the icon
	var texture = load(definition.icon_path)
	if texture:
		icon.texture = texture
	
	# Set icon color to help distinguish types (temporary visual aid)
	icon.modulate = definition.color
	
	# Store resource name for potential tooltip/UI
	tooltip_text = definition.name

func _on_lifetime_timer_timeout():
	"""Start fading when lifetime expires"""
	if not is_fading:
		start_fade()

func start_fade():
	"""Begin the fade out process"""
	is_fading = true
	fade_timer.start()
	
	# Create fade tween
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, fade_duration)

func _on_fade_timer_timeout():
	"""Remove the node when fade is complete"""
	queue_free()

func _on_button_pressed():
	"""Handle resource collection when button is pressed"""
	# For now, just print what was collected
	# Later this will give actual resources to the player
	var definition = resource_definitions[resource_type]
	print("Collected: ", definition.name)
	
	# TODO: Add actual resource collection logic here
	# Examples:
	# - PlayerStats.add_fuel(50) for fuel
	# - PlayerStats.add_currency(25) for food
	# - PlayerStats.add_research_points(10) for people
	
	# Remove the node immediately when collected
	queue_free()

func force_cleanup():
	"""Force immediate cleanup of this node (called when new scan happens)"""
	if lifetime_timer:
		lifetime_timer.stop()
	if fade_timer:
		fade_timer.stop()
	queue_free()
