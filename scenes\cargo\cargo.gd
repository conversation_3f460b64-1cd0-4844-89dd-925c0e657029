extends Control

@onready var people_count_label: Label = %PeopleCountLabel
@onready var people_panel: Panel = %PeoplePanel

@onready var cargo_capacity_bar: ProgressBar = %CargoCapacityBar

@onready var cargo_processing_bar: ProgressBar = %CargoProcessingBar
@onready var eat_bar: ProgressBar = %EATBar
@onready var fuel_bar: ProgressBar = %FUELBar
@onready var disect_bar: ProgressBar = %DISECTBar
@onready var probe_bar: ProgressBar = %PROBEBar

@onready var eat_button: Button = %EatButton
@onready var fuel_button: Button = %FuelButton
@onready var disect_button: Button = %DisectButton
@onready var probe_button: Button = %ProbeButton

## Human sprite management
var human_sprites: Array[Control] = []
var human_icon_scene = preload("res://icon.svg")

## Processing queue system
var queued_eat: int = 0
var queued_fuel: int = 0
var queued_disect: int = 0
var queued_probe: int = 0

## Processing system
var currently_processing: bool = false
var current_processing_type: String = ""
var processing_timer: float = 0.0
var processing_max_time: float = 0.0
var processing_sprite: Control = null
var processing_beam: Control = null

## Processing colors (customizable)
var eat_color: Color = Color.RED
var fuel_color: Color = Color.ORANGE
var disect_color: Color = Color.GREEN
var probe_color: Color = Color.BLUE

func _ready():
	## Initialize cargo system
	# Connect to cargo changes
	SignalBus.cargo_changed.connect(_on_cargo_changed)

func _process(delta):
	## Handle processing timer updates
	if currently_processing:
		processing_timer += delta

		# Update processing bar
		cargo_processing_bar.max_value = processing_max_time
		cargo_processing_bar.value = processing_timer

		# Check if processing is complete
		if processing_timer >= processing_max_time:
			complete_current_processing()

	# Connect button signals
	eat_button.pressed.connect(_on_eat_button_pressed)
	fuel_button.pressed.connect(_on_fuel_button_pressed)
	disect_button.pressed.connect(_on_disect_button_pressed)
	probe_button.pressed.connect(_on_probe_button_pressed)

	# Connect to location arrival for follower release
	SignalBus.location_arrived.connect(_on_location_arrived)

	# Initialize display
	update_cargo_display()
	update_button_states()
	update_cargo_bars()

	# Regenerate sprites for any existing cargo (important for save/load)
	regenerate_cargo_sprites()

func _on_cargo_changed(current_humans: int, current_followers: int):
	## Handle cargo changes from PlayerStats
	update_cargo_display()
	update_button_states()
	update_cargo_bars()

	# Adjust sprite count to match cargo
	adjust_human_sprites(current_humans, current_followers)

func update_cargo_display():
	## Update the people count label with [current / capacity] format
	var current_formatted = format_number_with_commas(PlayerStats.current_cargo_humans)
	var capacity_formatted = format_number_with_commas(PlayerStats.ship_human_capacity)
	people_count_label.text = "[%s / %s]" % [current_formatted, capacity_formatted]

	# Update capacity bar
	cargo_capacity_bar.max_value = PlayerStats.ship_human_capacity
	cargo_capacity_bar.value = PlayerStats.current_cargo_humans

func update_cargo_bars():
	## Update all cargo processing bars with stacked visual representation
	var capacity = PlayerStats.ship_human_capacity
	var current_cargo = PlayerStats.current_cargo_humans

	# Set all bars to same max value
	cargo_capacity_bar.max_value = capacity
	eat_bar.max_value = capacity
	fuel_bar.max_value = capacity
	disect_bar.max_value = capacity
	probe_bar.max_value = capacity

	# Main bar shows total cargo
	cargo_capacity_bar.value = current_cargo

	# Calculate stacked values (priority: eat > fuel > disect > probe)
	# Each bar shows how much cargo is NOT allocated to higher priority actions
	var remaining_after_eat = max(0, current_cargo - queued_eat)
	var remaining_after_fuel = max(0, remaining_after_eat - queued_fuel)
	var remaining_after_disect = max(0, remaining_after_fuel - queued_disect)
	var remaining_after_probe = max(0, remaining_after_disect - queued_probe)

	# Set bar values (each shows what's left after higher priority allocations)
	eat_bar.value = remaining_after_eat
	fuel_bar.value = remaining_after_fuel
	disect_bar.value = remaining_after_disect
	probe_bar.value = remaining_after_probe

func format_number_with_commas(number: int) -> String:
	## Format a number with comma separators (e.g., 1000 -> 1,000)
	var number_str = str(number)
	var result = ""
	var count = 0

	# Process digits from right to left
	for i in range(number_str.length() - 1, -1, -1):
		if count > 0 and count % 3 == 0:
			result = "," + result
		result = number_str[i] + result
		count += 1

	return result

func update_button_states():
	## Update button enabled/disabled states based on cargo and queue
	var current_cargo = PlayerStats.current_cargo_humans
	var total_queued = get_total_queued()
	var available_for_processing = current_cargo - total_queued
	var has_non_followers = PlayerStats.get_non_followers_in_cargo() > 0

	# Eat, Fuel, Disect buttons are enabled if we have unqueued humans
	eat_button.disabled = available_for_processing <= 0
	fuel_button.disabled = available_for_processing <= 0
	disect_button.disabled = available_for_processing <= 0

	# Probe button is only enabled if we have non-followers and available cargo
	probe_button.disabled = not has_non_followers or available_for_processing <= 0

func can_queue_action(action_type: String) -> bool:
	## Check if we can queue an action based on available cargo
	var current_cargo = PlayerStats.current_cargo_humans
	var total_queued = get_total_queued()
	var available_for_processing = current_cargo - total_queued

	if available_for_processing <= 0:
		return false

	# Special check for probe - need non-followers
	if action_type == "probe":
		return PlayerStats.get_non_followers_in_cargo() > 0

	return true

func get_total_queued() -> int:
	## Get total number of queued actions
	return queued_eat + queued_fuel + queued_disect + queued_probe

## Button Handler Functions
func _on_eat_button_pressed():
	## Queue a human for food processing
	if can_queue_action("eat"):
		queued_eat += 1
		update_cargo_bars()
		update_button_states()
		print("Queued human for food processing. Queue: %d" % queued_eat)

		# Start processing if not already processing
		if not currently_processing:
			start_next_processing()

func _on_fuel_button_pressed():
	## Queue a human for fuel processing
	if can_queue_action("fuel"):
		queued_fuel += 1
		update_cargo_bars()
		update_button_states()
		print("Queued human for fuel processing. Queue: %d" % queued_fuel)

		# Start processing if not already processing
		if not currently_processing:
			start_next_processing()

func _on_disect_button_pressed():
	## Queue a human for research processing
	if can_queue_action("disect"):
		queued_disect += 1
		update_cargo_bars()
		update_button_states()
		print("Queued human for dissection. Queue: %d" % queued_disect)

		# Start processing if not already processing
		if not currently_processing:
			start_next_processing()

func _on_probe_button_pressed():
	## Queue a human for follower conversion
	if can_queue_action("probe"):
		queued_probe += 1
		update_cargo_bars()
		update_button_states()
		print("Queued human for probing. Queue: %d" % queued_probe)

		# Start processing if not already processing
		if not currently_processing:
			start_next_processing()

func _on_location_arrived(_resource_type: int, _resource_node: Control):
	## Release followers when arriving at any location
	PlayerStats.release_followers_at_location()

## Processing Queue Management
func process_next_in_queue():
	## Process the next action in queue based on priority (eat > fuel > disect > probe)
	if queued_eat > 0:
		if PlayerStats.consume_human_for_food():
			queued_eat -= 1
			print("Processed human for food. Remaining in queue: %d" % queued_eat)
			update_cargo_bars()
			update_button_states()
			return true
	elif queued_fuel > 0:
		if PlayerStats.consume_human_for_fuel():
			queued_fuel -= 1
			print("Processed human for fuel. Remaining in queue: %d" % queued_fuel)
			update_cargo_bars()
			update_button_states()
			return true
	elif queued_disect > 0:
		if PlayerStats.consume_human_for_research():
			queued_disect -= 1
			print("Processed human for research. Remaining in queue: %d" % queued_disect)
			update_cargo_bars()
			update_button_states()
			return true
	elif queued_probe > 0:
		if PlayerStats.convert_human_to_follower():
			queued_probe -= 1
			print("Processed human for probing. Remaining in queue: %d" % queued_probe)
			update_cargo_bars()
			update_button_states()
			return true

	return false

func clear_all_queues():
	## Clear all processing queues (useful for emergencies or resets)
	queued_eat = 0
	queued_fuel = 0
	queued_disect = 0
	queued_probe = 0
	update_cargo_bars()
	update_button_states()
	print("All processing queues cleared")

## Processing System Functions
func start_next_processing():
	## Start processing the next queued action
	if currently_processing:
		return

	var next_action = get_next_action_to_process()
	if next_action == "":
		return

	# Select a human sprite to process (prioritize non-followers except for probe)
	var sprite_to_process = select_human_for_processing(next_action)
	if sprite_to_process == null:
		print("No suitable human found for processing: ", next_action)
		return

	# Start the processing
	current_processing_type = next_action
	processing_sprite = sprite_to_process
	processing_timer = 0.0
	currently_processing = true

	# Set processing time and bar color based on action
	match next_action:
		"eat":
			processing_max_time = PlayerStats.human_eat_time
			cargo_processing_bar.modulate = eat_color
			queued_eat -= 1
		"fuel":
			processing_max_time = PlayerStats.human_fuel_time
			cargo_processing_bar.modulate = fuel_color
			queued_fuel -= 1
		"disect":
			processing_max_time = PlayerStats.human_disect_time
			cargo_processing_bar.modulate = disect_color
			queued_disect -= 1
		"probe":
			processing_max_time = PlayerStats.human_probe_time
			cargo_processing_bar.modulate = probe_color
			queued_probe -= 1

	# Create and animate the tractor beam
	create_tractor_beam(sprite_to_process)

	# Update displays
	update_cargo_bars()
	update_button_states()

	print("Started processing human for: ", next_action, " (", processing_max_time, "s)")

func get_next_action_to_process() -> String:
	## Get the next action to process based on priority
	if queued_eat > 0:
		return "eat"
	elif queued_fuel > 0:
		return "fuel"
	elif queued_disect > 0:
		return "disect"
	elif queued_probe > 0:
		return "probe"
	return ""

func select_human_for_processing(action_type: String) -> Control:
	## Select a human sprite for processing (prioritize non-followers except for probe)
	if human_sprites.size() == 0:
		return null

	var followers_in_cargo = PlayerStats.current_cargo_followers
	var total_humans = human_sprites.size()

	if action_type == "probe":
		# For probing, we need a non-follower (first N sprites are followers)
		if followers_in_cargo < total_humans:
			# Return first non-follower sprite
			return human_sprites[followers_in_cargo]
		else:
			return null
	else:
		# For other actions, prioritize non-followers first
		if followers_in_cargo < total_humans:
			# Return first non-follower sprite
			return human_sprites[followers_in_cargo]
		else:
			# Only followers left, take the first one
			return human_sprites[0]

func create_tractor_beam(sprite: Control):
	## Create a tractor beam effect for the processing sprite
	# Stop the sprite's horizontal movement
	if sprite.has_method("stop_movement"):
		sprite.stop_movement()

	# Create beam visual (ColorRect with gradient)
	processing_beam = ColorRect.new()
	processing_beam.size = Vector2(20, people_panel.size.y)
	processing_beam.position = Vector2(sprite.position.x - 10, 0)

	# Create gradient material for beam effect
	var gradient = Gradient.new()
	gradient.add_point(0.0, Color(0, 1, 0, 0.8))  # Green edges
	gradient.add_point(0.5, Color(0, 1, 0, 0.1))  # Transparent center
	gradient.add_point(1.0, Color(0, 1, 0, 0.8))  # Green edges

	var gradient_texture = GradientTexture2D.new()
	gradient_texture.gradient = gradient
	gradient_texture.fill_from = Vector2(0, 0.5)
	gradient_texture.fill_to = Vector2(1, 0.5)

	processing_beam.texture = gradient_texture
	people_panel.add_child(processing_beam)

	# Animate sprite upward and out of view
	var tween = create_tween()
	tween.tween_property(sprite, "position:y", -50, 2.0).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_CUBIC)
	tween.tween_callback(sprite.queue_free)
	tween.tween_callback(remove_sprite_from_array.bind(sprite))

	# For non-probe actions, free up cargo space immediately
	if current_processing_type != "probe":
		PlayerStats.current_cargo_humans -= 1
		if is_follower_sprite(sprite):
			PlayerStats.current_cargo_followers -= 1
		SignalBus.emit_cargo_changed(PlayerStats.current_cargo_humans, PlayerStats.current_cargo_followers)

	# Fade out beam after sprite is gone
	tween.tween_property(processing_beam, "modulate:a", 0.0, 1.0)
	tween.tween_callback(processing_beam.queue_free)

func remove_sprite_from_array(sprite: Control):
	## Remove sprite from the human_sprites array
	var index = human_sprites.find(sprite)
	if index >= 0:
		human_sprites.remove_at(index)

func is_follower_sprite(sprite: Control) -> bool:
	## Check if a sprite represents a follower (first N sprites are followers)
	var index = human_sprites.find(sprite)
	if index >= 0:
		return index < PlayerStats.current_cargo_followers
	return false

func complete_current_processing():
	## Complete the current processing and grant rewards
	if not currently_processing:
		return

	# Grant the resource reward
	match current_processing_type:
		"eat":
			PlayerStats.add_food_instantly(PlayerStats.human_food_value)
			PlayerStats.reduce_human_population(1)
			print("Processing complete: +%d food" % PlayerStats.human_food_value)
		"fuel":
			PlayerStats.add_fuel_instantly(PlayerStats.human_fuel_value)
			PlayerStats.reduce_human_population(1)
			print("Processing complete: +%d fuel" % PlayerStats.human_fuel_value)
		"disect":
			PlayerStats.add_research_points(PlayerStats.human_research_value)
			PlayerStats.reduce_human_population(1)
			print("Processing complete: +%d research" % PlayerStats.human_research_value)
		"probe":
			# For probe, the human becomes a follower and returns to cargo
			PlayerStats.current_cargo_followers += 1
			PlayerStats.add_followers(1)
			SignalBus.emit_cargo_changed(PlayerStats.current_cargo_humans, PlayerStats.current_cargo_followers)

			# Create a new blue sprite falling back into cargo
			create_returning_follower_sprite()
			print("Processing complete: Human converted to follower")

	# Reset processing state
	currently_processing = false
	current_processing_type = ""
	processing_timer = 0.0
	processing_max_time = 0.0
	processing_sprite = null
	processing_beam = null

	# Reset processing bar
	cargo_processing_bar.value = 0
	cargo_processing_bar.modulate = Color.WHITE

	# Update displays
	update_cargo_bars()
	update_button_states()

	# Start next processing if queue has items
	if get_total_queued() > 0:
		start_next_processing()

func create_returning_follower_sprite():
	## Create a follower sprite that falls back into cargo after probing
	var sprite = TextureRect.new()
	sprite.texture = human_icon_scene
	sprite.custom_minimum_size = Vector2(5, 12)
	sprite.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	sprite.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	sprite.modulate = Color.CYAN  # Blue for follower

	# Position at top of panel
	var panel_rect = people_panel.get_rect()
	var random_x = randf_range(0, panel_rect.size.x - 5)
	sprite.position = Vector2(random_x, -12)

	people_panel.add_child(sprite)
	human_sprites.append(sprite)

	# Animate drop to bottom
	var target_y = panel_rect.size.y - 12
	var tween = create_tween()
	tween.tween_property(sprite, "position:y", target_y, 0.5).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BOUNCE)

	# Start horizontal movement
	start_horizontal_movement(sprite)

## Human Sprite Management
func adjust_human_sprites(target_humans: int, target_followers: int):
	## Adjust the number of human sprites to match cargo count
	var current_sprite_count = human_sprites.size()

	if target_humans > current_sprite_count:
		# Add new sprites
		for i in range(target_humans - current_sprite_count):
			add_human_sprite()
	elif target_humans < current_sprite_count:
		# Remove excess sprites
		for i in range(current_sprite_count - target_humans):
			remove_human_sprite()

	# Update follower colors
	update_follower_colors(target_followers)

func regenerate_cargo_sprites():
	## Regenerate all cargo sprites (used on game load)
	# Clear any existing sprites first
	for sprite in human_sprites:
		if is_instance_valid(sprite):
			sprite.queue_free()
	human_sprites.clear()

	# Get current cargo counts from PlayerStats
	var current_humans = PlayerStats.current_cargo_humans
	var current_followers = PlayerStats.current_cargo_followers

	# Generate sprites without drop animation (they're already "loaded")
	for i in range(current_humans):
		add_human_sprite_no_animation()

	# Update follower colors
	update_follower_colors(current_followers)

func add_human_sprite_no_animation():
	## Add a human sprite without drop animation (for loading)
	var sprite = TextureRect.new()
	sprite.texture = human_icon_scene
	sprite.custom_minimum_size = Vector2(5, 12)
	sprite.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	sprite.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

	# Position randomly within panel bounds (already "landed")
	var panel_rect = people_panel.get_rect()
	var random_x = randf_range(0, panel_rect.size.x - 5)
	var bottom_y = panel_rect.size.y - 12
	sprite.position = Vector2(random_x, bottom_y)

	people_panel.add_child(sprite)
	human_sprites.append(sprite)

	# Start horizontal movement
	start_horizontal_movement(sprite)

func add_human_sprite():
	## Add a new human sprite with drop animation
	var sprite = TextureRect.new()
	sprite.texture = human_icon_scene
	sprite.custom_minimum_size = Vector2(5, 12)
	sprite.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	sprite.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

	# Position randomly at top of panel
	var panel_rect = people_panel.get_rect()
	var random_x = randf_range(0, panel_rect.size.x - 5)
	sprite.position = Vector2(random_x, -12)  # Start above panel

	people_panel.add_child(sprite)
	human_sprites.append(sprite)

	# Animate drop to bottom
	var target_y = panel_rect.size.y - 12
	var tween = create_tween()
	tween.tween_property(sprite, "position:y", target_y, 0.5).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BOUNCE)

	# Start horizontal movement
	start_horizontal_movement(sprite)

func remove_human_sprite():
	## Remove the last human sprite
	if human_sprites.size() > 0:
		var sprite = human_sprites.pop_back()
		sprite.queue_free()

func start_horizontal_movement(sprite: Control):
	## Start continuous horizontal movement for a sprite
	var panel_rect = people_panel.get_rect()
	var movement_speed = 2.0  # pixels per second

	# Create a looping tween for horizontal movement
	var move_tween = create_tween()
	move_tween.set_loops()

	# Random initial direction
	var direction = 1 if randf() > 0.5 else -1

	# Calculate movement bounds
	var left_bound = 0.0
	var right_bound = panel_rect.size.x - 5.0

	# Start movement cycle
	animate_horizontal_movement(sprite, move_tween, direction, movement_speed, left_bound, right_bound)

func animate_horizontal_movement(sprite: Control, tween: Tween, direction: int, speed: float, left_bound: float, right_bound: float):
	## Animate horizontal movement with boundary bouncing
	var current_x = sprite.position.x
	var target_x: float
	var duration: float

	if direction > 0:
		# Moving right
		target_x = right_bound
		duration = (target_x - current_x) / speed
	else:
		# Moving left
		target_x = left_bound
		duration = (current_x - target_x) / speed

	# Animate to target
	tween.tween_property(sprite, "position:x", target_x, duration)

	# When complete, reverse direction and continue
	tween.tween_callback(func():
		if sprite and is_instance_valid(sprite):
			animate_horizontal_movement(sprite, tween, -direction, speed, left_bound, right_bound)
	)

func update_follower_colors(follower_count: int):
	## Update sprite colors to show followers (blue) vs regular humans (white)
	for i in range(human_sprites.size()):
		var sprite = human_sprites[i]
		if i < follower_count:
			# This is a follower - make it blue
			sprite.modulate = Color.CYAN
		else:
			# Regular human - keep white
			sprite.modulate = Color.WHITE
