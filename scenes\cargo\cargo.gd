extends Control

@onready var people_count_label: Label = %PeopleCountLabel
@onready var people_panel: Panel = %PeoplePanel

@onready var cargo_capacity_bar: ProgressBar = %CargoCapacityBar

@onready var cargo_processing_bar: ProgressBar = %CargoProcessingBar
@onready var eat_bar: ProgressBar = %EATBar
@onready var fuel_bar: ProgressBar = %FUELBar
@onready var disect_bar: ProgressBar = %DISECTBar
@onready var probe_bar: ProgressBar = %PROBEBar

@onready var eat_button: Button = %EatButton
@onready var fuel_button: Button = %FuelButton
@onready var disect_button: Button = %DisectButton
@onready var probe_button: Button = %ProbeButton

## Human sprite management
var human_sprites: Array[Control] = []
var human_icon_scene = preload("res://icon.svg")

func _ready():
	## Initialize cargo system
	# Connect to cargo changes
	SignalBus.cargo_changed.connect(_on_cargo_changed)

	# Connect button signals
	eat_button.pressed.connect(_on_eat_button_pressed)
	fuel_button.pressed.connect(_on_fuel_button_pressed)
	disect_button.pressed.connect(_on_disect_button_pressed)
	probe_button.pressed.connect(_on_probe_button_pressed)

	# Connect to location arrival for follower release
	SignalBus.location_arrived.connect(_on_location_arrived)

	# Initialize display
	update_cargo_display()
	update_button_states()

	# Regenerate sprites for any existing cargo (important for save/load)
	regenerate_cargo_sprites()

func _on_cargo_changed(current_humans: int, current_followers: int):
	## Handle cargo changes from PlayerStats
	update_cargo_display()
	update_button_states()

	# Adjust sprite count to match cargo
	adjust_human_sprites(current_humans, current_followers)

func update_cargo_display():
	## Update the people count label with [current / capacity] format
	var current_formatted = format_number_with_commas(PlayerStats.current_cargo_humans)
	var capacity_formatted = format_number_with_commas(PlayerStats.ship_human_capacity)
	people_count_label.text = "[%s / %s]" % [current_formatted, capacity_formatted]

	# Update capacity bar
	cargo_capacity_bar.max_value = PlayerStats.ship_human_capacity
	cargo_capacity_bar.value = PlayerStats.current_cargo_humans

func format_number_with_commas(number: int) -> String:
	## Format a number with comma separators (e.g., 1000 -> 1,000)
	var number_str = str(number)
	var result = ""
	var count = 0

	# Process digits from right to left
	for i in range(number_str.length() - 1, -1, -1):
		if count > 0 and count % 3 == 0:
			result = "," + result
		result = number_str[i] + result
		count += 1

	return result

func update_button_states():
	## Update button enabled/disabled states based on cargo
	var has_humans = PlayerStats.current_cargo_humans > 0
	var has_non_followers = PlayerStats.get_non_followers_in_cargo() > 0

	# Eat, Fuel, Disect buttons are enabled if we have any humans
	eat_button.disabled = not has_humans
	fuel_button.disabled = not has_humans
	disect_button.disabled = not has_humans

	# Probe button is only enabled if we have non-followers
	probe_button.disabled = not has_non_followers

## Button Handler Functions
func _on_eat_button_pressed():
	## Consume a human for food
	if PlayerStats.consume_human_for_food():
		print("Human consumed for food")

func _on_fuel_button_pressed():
	## Consume a human for fuel
	if PlayerStats.consume_human_for_fuel():
		print("Human processed for fuel")

func _on_disect_button_pressed():
	## Dissect a human for research
	if PlayerStats.consume_human_for_research():
		print("Human dissected for research")

func _on_probe_button_pressed():
	## Convert a human to follower
	if PlayerStats.convert_human_to_follower():
		print("Human converted to follower")

func _on_location_arrived(_resource_type: int, _resource_node: Control):
	## Release followers when arriving at any location
	PlayerStats.release_followers_at_location()

## Human Sprite Management
func adjust_human_sprites(target_humans: int, target_followers: int):
	## Adjust the number of human sprites to match cargo count
	var current_sprite_count = human_sprites.size()

	if target_humans > current_sprite_count:
		# Add new sprites
		for i in range(target_humans - current_sprite_count):
			add_human_sprite()
	elif target_humans < current_sprite_count:
		# Remove excess sprites
		for i in range(current_sprite_count - target_humans):
			remove_human_sprite()

	# Update follower colors
	update_follower_colors(target_followers)

func regenerate_cargo_sprites():
	## Regenerate all cargo sprites (used on game load)
	# Clear any existing sprites first
	for sprite in human_sprites:
		if is_instance_valid(sprite):
			sprite.queue_free()
	human_sprites.clear()

	# Get current cargo counts from PlayerStats
	var current_humans = PlayerStats.current_cargo_humans
	var current_followers = PlayerStats.current_cargo_followers

	# Generate sprites without drop animation (they're already "loaded")
	for i in range(current_humans):
		add_human_sprite_no_animation()

	# Update follower colors
	update_follower_colors(current_followers)

func add_human_sprite_no_animation():
	## Add a human sprite without drop animation (for loading)
	var sprite = TextureRect.new()
	sprite.texture = human_icon_scene
	sprite.custom_minimum_size = Vector2(5, 12)
	sprite.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	sprite.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

	# Position randomly within panel bounds (already "landed")
	var panel_rect = people_panel.get_rect()
	var random_x = randf_range(0, panel_rect.size.x - 5)
	var bottom_y = panel_rect.size.y - 12
	sprite.position = Vector2(random_x, bottom_y)

	people_panel.add_child(sprite)
	human_sprites.append(sprite)

	# Start horizontal movement
	start_horizontal_movement(sprite)

func add_human_sprite():
	## Add a new human sprite with drop animation
	var sprite = TextureRect.new()
	sprite.texture = human_icon_scene
	sprite.custom_minimum_size = Vector2(5, 12)
	sprite.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	sprite.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

	# Position randomly at top of panel
	var panel_rect = people_panel.get_rect()
	var random_x = randf_range(0, panel_rect.size.x - 5)
	sprite.position = Vector2(random_x, -12)  # Start above panel

	people_panel.add_child(sprite)
	human_sprites.append(sprite)

	# Animate drop to bottom
	var target_y = panel_rect.size.y - 12
	var tween = create_tween()
	tween.tween_property(sprite, "position:y", target_y, 0.5).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BOUNCE)

	# Start horizontal movement
	start_horizontal_movement(sprite)

func remove_human_sprite():
	## Remove the last human sprite
	if human_sprites.size() > 0:
		var sprite = human_sprites.pop_back()
		sprite.queue_free()

func start_horizontal_movement(sprite: Control):
	## Start continuous horizontal movement for a sprite
	var panel_rect = people_panel.get_rect()
	var movement_speed = 2.0  # pixels per second

	# Create a looping tween for horizontal movement
	var move_tween = create_tween()
	move_tween.set_loops()

	# Random initial direction
	var direction = 1 if randf() > 0.5 else -1

	# Calculate movement bounds
	var left_bound = 0.0
	var right_bound = panel_rect.size.x - 5.0

	# Start movement cycle
	animate_horizontal_movement(sprite, move_tween, direction, movement_speed, left_bound, right_bound)

func animate_horizontal_movement(sprite: Control, tween: Tween, direction: int, speed: float, left_bound: float, right_bound: float):
	## Animate horizontal movement with boundary bouncing
	var current_x = sprite.position.x
	var target_x: float
	var duration: float

	if direction > 0:
		# Moving right
		target_x = right_bound
		duration = (target_x - current_x) / speed
	else:
		# Moving left
		target_x = left_bound
		duration = (current_x - target_x) / speed

	# Animate to target
	tween.tween_property(sprite, "position:x", target_x, duration)

	# When complete, reverse direction and continue
	tween.tween_callback(func():
		if sprite and is_instance_valid(sprite):
			animate_horizontal_movement(sprite, tween, -direction, speed, left_bound, right_bound)
	)

func update_follower_colors(follower_count: int):
	## Update sprite colors to show followers (blue) vs regular humans (white)
	for i in range(human_sprites.size()):
		var sprite = human_sprites[i]
		if i < follower_count:
			# This is a follower - make it blue
			sprite.modulate = Color.CYAN
		else:
			# Regular human - keep white
			sprite.modulate = Color.WHITE
