extends Control

@onready var scan_button: Button = %ScanButton
@onready var scanner_ui: Panel = %ScannerUI

# Radar system variables
var radar_pulses: Array[Control] = []
var pulse_timer: Timer
var scan_cooldown_timer: Timer
var is_scan_on_cooldown: bool = false

# Radar settings - configurable
var pulse_growth_rate: float = 10.0 # pixels per second
var pulse_visible_duration: float = 10.0 # seconds fully visible
var pulse_fade_duration: float = 3.0 # seconds to fade out
var pulse_spawn_min: float = 1.5 # minimum seconds between pulses
var pulse_spawn_max: float = 5.0 # maximum seconds between pulses
var crosshair_width: float = 2.0 # width of crosshair lines
var pulse_outline_width: float = 0.5 # width of pulse outlines

# Scan wave settings (unique player-triggered wave)
var scan_wave_growth_rate: float = 50.0 # faster than regular pulses
var scan_wave_visible_duration: float = 2.0 # longer visibility
var scan_wave_fade_duration: float = 2.0 # same fade time
var scan_wave_outline_width: float = 1.0 # thicker line

func _ready():
	setup_radar_system()
	setup_scan_button()

func setup_radar_system():
	# Enable clipping on the scanner panel
	scanner_ui.clip_contents = true

	# Create static crosshair lines
	create_crosshair_lines()
	
	# Wait for crosshairs to be set before moving on
	await get_tree().process_frame

	# Setup pulse timer for random intervals
	pulse_timer = Timer.new()
	pulse_timer.wait_time = randf_range(pulse_spawn_min, pulse_spawn_max)
	pulse_timer.timeout.connect(_on_pulse_timer_timeout)
	pulse_timer.autostart = true
	add_child(pulse_timer)

	# Start the radar system
	start_radar_pulses()

func setup_scan_button():
	# Connect scan button to trigger function
	scan_button.pressed.connect(_on_scan_button_pressed)

	# Setup scan cooldown timer
	scan_cooldown_timer = Timer.new()
	scan_cooldown_timer.one_shot = true
	scan_cooldown_timer.timeout.connect(_on_scan_cooldown_finished)
	add_child(scan_cooldown_timer)

func _on_scan_button_pressed():
	# Check if scan is on cooldown
	if is_scan_on_cooldown:
		return

	# Trigger the unique scan wave
	create_scan_wave()

	# Start cooldown
	start_scan_cooldown()

	# Emit signal for other systems to react
	var panel_size = scanner_ui.size
	var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)
	SignalBus.emit_player_scan_triggered(center)

func start_scan_cooldown():
	is_scan_on_cooldown = true
	scan_button.disabled = true
	scan_cooldown_timer.wait_time = PlayerStats.scan_cooldown
	scan_cooldown_timer.start()

	# Emit cooldown started signal
	SignalBus.emit_scan_cooldown_started(PlayerStats.scan_cooldown)

func _on_scan_cooldown_finished():
	is_scan_on_cooldown = false
	scan_button.disabled = false

	# Emit cooldown finished signal
	SignalBus.emit_scan_cooldown_finished()

func create_scan_wave():
	# Create a unique scan wave with different properties
	var scan_wave = RadarPulse.new()
	scan_wave.setup(scan_wave_growth_rate, scan_wave_visible_duration, scan_wave_fade_duration, scan_wave_outline_width)

	# Position at center of scanner panel
	var panel_size = scanner_ui.size
	var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)
	scan_wave.position = center

	# Add to scanner panel and track it
	scanner_ui.add_child(scan_wave)
	radar_pulses.append(scan_wave)

	# Connect cleanup signal
	scan_wave.pulse_finished.connect(_on_pulse_finished.bind(scan_wave))

func create_crosshair_lines():
	# Create vertical line
	var vertical_line = ColorRect.new()
	vertical_line.color = Color.WHITE
	vertical_line.name = "VerticalCrosshair"
	scanner_ui.add_child(vertical_line)

	# Create horizontal line
	var horizontal_line = ColorRect.new()
	horizontal_line.color = Color.WHITE
	horizontal_line.name = "HorizontalCrosshair"
	scanner_ui.add_child(horizontal_line)

	# Position and size the crosshair lines (will be updated in _process)
	update_crosshair_positions()

func update_crosshair_positions():
	var panel_size = scanner_ui.size
	var center_x = panel_size.x / 2.0
	var center_y = panel_size.y / 2.0

	# Update vertical line
	var vertical_line = scanner_ui.get_node("VerticalCrosshair")
	if vertical_line:
		vertical_line.position = Vector2(center_x - crosshair_width / 2.0, 0)
		vertical_line.size = Vector2(crosshair_width, panel_size.y)

	# Update horizontal line
	var horizontal_line = scanner_ui.get_node("HorizontalCrosshair")
	if horizontal_line:
		horizontal_line.position = Vector2(0, center_y - crosshair_width / 2.0)
		horizontal_line.size = Vector2(panel_size.x, crosshair_width)

func start_radar_pulses():
	# Create the first pulse immediately
	create_radar_pulse()

func _on_pulse_timer_timeout():
	# Create a new radar pulse
	create_radar_pulse()

	# Set random time for next pulse
	pulse_timer.wait_time = randf_range(pulse_spawn_min, pulse_spawn_max)

func create_radar_pulse():
	# Create a custom Control node for the radar pulse
	var pulse = RadarPulse.new()
	pulse.setup(pulse_growth_rate, pulse_visible_duration, pulse_fade_duration, pulse_outline_width)

	# Position at center of scanner panel
	var panel_size = scanner_ui.size
	var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)
	pulse.position = center

	# Add to scanner panel and track it
	scanner_ui.add_child(pulse)
	radar_pulses.append(pulse)

	# Connect cleanup signal
	pulse.pulse_finished.connect(_on_pulse_finished.bind(pulse))

func _on_pulse_finished(pulse: Control):
	# Remove from tracking array
	var index = radar_pulses.find(pulse)
	if index != -1:
		radar_pulses.remove_at(index)

func _process(_delta):
	# Update crosshair positions in case panel size changes
	update_crosshair_positions()

# Custom RadarPulse inner class
class RadarPulse extends Control:
	signal pulse_finished

	var growth_rate: float
	var visible_duration: float
	var fade_duration: float
	var outline_width: float
	var current_radius: float = 0.0
	var life_timer: float = 0.0
	var is_fading: bool = false

	func setup(p_growth_rate: float, p_visible_duration: float, p_fade_duration: float, p_outline_width: float):
		growth_rate = p_growth_rate
		visible_duration = p_visible_duration
		fade_duration = p_fade_duration
		outline_width = p_outline_width

		# Set initial properties
		modulate = Color.WHITE

	func _ready():
		# Ensure the pulse draws itself
		custom_minimum_size = Vector2(1000, 1000) # Large enough for any reasonable radar

	func _process(delta):
		life_timer += delta

		# Grow the radius
		current_radius += growth_rate * delta

		# Check if we should start fading
		if life_timer >= visible_duration and not is_fading:
			is_fading = true
			start_fade_out()

		# Force redraw
		queue_redraw()

	func _draw():
		# Draw circle outline with transparent fill
		if current_radius > 0:
			draw_arc(Vector2.ZERO, current_radius, 0, TAU, 64, Color.WHITE, outline_width, true)

	func start_fade_out():
		# Create tween for fade out
		var tween = create_tween()
		tween.tween_property(self, "modulate:a", 0.0, fade_duration)
		tween.tween_callback(finish_pulse)

	func finish_pulse():
		pulse_finished.emit()
		queue_free()
