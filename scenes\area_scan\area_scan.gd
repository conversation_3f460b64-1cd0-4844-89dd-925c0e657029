extends Control

@onready var scan_button: Button = %ScanButton
@onready var scanner_ui: Panel = %ScannerUI

# Radar system variables
var radar_pulses: Array[Control] = []
var pulse_timer: Timer
var scan_cooldown_timer: Timer
var is_scan_on_cooldown: bool = false

# Resource node system
var active_resource_nodes: Array[Control] = []
var active_reveal_timers: Array[Timer] = []
var area_node_scene = preload("res://scenes/area_scan/area_node.tscn")

# Resource generation settings
var min_resources_per_scan: int = 2
var max_resources_per_scan: int = 5

# Movement system
var selected_resource_node: Control = null
var is_moving_to_resource: bool = false
var movement_direction: Vector2 = Vector2.ZERO
var movement_distance_remaining: float = 0.0

# Radar settings - configurable
var pulse_growth_rate: float = 10.0 # pixels per second
var pulse_visible_duration: float = 10.0 # seconds fully visible
var pulse_fade_duration: float = 3.0 # seconds to fade out
var pulse_spawn_min: float = 1.5 # minimum seconds between pulses
var pulse_spawn_max: float = 5.0 # maximum seconds between pulses
var crosshair_width: float = 2.0 # width of crosshair lines
var pulse_outline_width: float = 0.5 # width of pulse outlines

# Scan wave settings (unique player-triggered wave)
var scan_wave_growth_rate: float = 50.0 # faster than regular pulses
var scan_wave_visible_duration: float = 2.0 # longer visibility
var scan_wave_fade_duration: float = 2.0 # same fade time
var scan_wave_outline_width: float = 1.0 # thicker line

func _ready():
	setup_radar_system()
	setup_scan_button()

func setup_radar_system():
	# Enable clipping on the scanner panel
	scanner_ui.clip_contents = true

	# Create static crosshair lines
	create_crosshair_lines()
	
	# Wait for crosshairs to be set before moving on
	await get_tree().process_frame

	# Setup pulse timer for random intervals
	pulse_timer = Timer.new()
	pulse_timer.wait_time = randf_range(pulse_spawn_min, pulse_spawn_max)
	pulse_timer.timeout.connect(_on_pulse_timer_timeout)
	pulse_timer.autostart = true
	add_child(pulse_timer)

	# Start the radar system
	start_radar_pulses()

func setup_scan_button():
	# Connect scan button to trigger function
	scan_button.pressed.connect(_on_scan_button_pressed)

	# Setup scan cooldown timer
	scan_cooldown_timer = Timer.new()
	scan_cooldown_timer.one_shot = true
	scan_cooldown_timer.timeout.connect(_on_scan_cooldown_finished)
	add_child(scan_cooldown_timer)

func _on_scan_button_pressed():
	# Check if scan is on cooldown
	if is_scan_on_cooldown:
		return

	# Clear any existing resource nodes
	clear_resource_nodes()

	# Trigger the unique scan wave
	create_scan_wave()

	# Generate new resource nodes
	generate_resource_nodes()

	# Start cooldown
	start_scan_cooldown()

	# Emit signal for other systems to react
	var panel_size = scanner_ui.size
	var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)
	SignalBus.emit_player_scan_triggered(center)

func start_scan_cooldown():
	is_scan_on_cooldown = true
	scan_button.disabled = true
	scan_cooldown_timer.wait_time = PlayerStats.scan_cooldown
	scan_cooldown_timer.start()

	# Emit cooldown started signal
	SignalBus.emit_scan_cooldown_started(PlayerStats.scan_cooldown)

func _on_scan_cooldown_finished():
	is_scan_on_cooldown = false
	scan_button.disabled = false

	# Emit cooldown finished signal
	SignalBus.emit_scan_cooldown_finished()

func create_scan_wave():
	# Create a unique scan wave with different properties
	var scan_wave = RadarPulse.new()
	scan_wave.setup(scan_wave_growth_rate, scan_wave_visible_duration, scan_wave_fade_duration, scan_wave_outline_width)

	# Position at center of scanner panel
	var panel_size = scanner_ui.size
	var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)
	scan_wave.position = center

	# Add to scanner panel and track it
	scanner_ui.add_child(scan_wave)
	radar_pulses.append(scan_wave)

	# Connect cleanup signal
	scan_wave.pulse_finished.connect(_on_pulse_finished.bind(scan_wave))

func create_crosshair_lines():
	# Create vertical line
	var vertical_line = ColorRect.new()
	vertical_line.color = Color.WHITE
	vertical_line.name = "VerticalCrosshair"
	scanner_ui.add_child(vertical_line)

	# Create horizontal line
	var horizontal_line = ColorRect.new()
	horizontal_line.color = Color.WHITE
	horizontal_line.name = "HorizontalCrosshair"
	scanner_ui.add_child(horizontal_line)

	# Position and size the crosshair lines (will be updated in _process)
	update_crosshair_positions()

func update_crosshair_positions():
	var panel_size = scanner_ui.size
	var center_x = panel_size.x / 2.0
	var center_y = panel_size.y / 2.0

	# Update vertical line
	var vertical_line = scanner_ui.get_node("VerticalCrosshair")
	if vertical_line:
		vertical_line.position = Vector2(center_x - crosshair_width / 2.0, 0)
		vertical_line.size = Vector2(crosshair_width, panel_size.y)

	# Update horizontal line
	var horizontal_line = scanner_ui.get_node("HorizontalCrosshair")
	if horizontal_line:
		horizontal_line.position = Vector2(0, center_y - crosshair_width / 2.0)
		horizontal_line.size = Vector2(panel_size.x, crosshair_width)

func start_radar_pulses():
	# Create the first pulse immediately
	create_radar_pulse()

func _on_pulse_timer_timeout():
	# Create a new radar pulse
	create_radar_pulse()

	# Set random time for next pulse
	pulse_timer.wait_time = randf_range(pulse_spawn_min, pulse_spawn_max)

func create_radar_pulse():
	# Create a custom Control node for the radar pulse
	var pulse = RadarPulse.new()
	pulse.setup(pulse_growth_rate, pulse_visible_duration, pulse_fade_duration, pulse_outline_width)

	# Position at center of scanner panel
	var panel_size = scanner_ui.size
	var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)
	pulse.position = center

	# Add to scanner panel and track it
	scanner_ui.add_child(pulse)
	radar_pulses.append(pulse)

	# Connect cleanup signal
	pulse.pulse_finished.connect(_on_pulse_finished.bind(pulse))

func _on_pulse_finished(pulse: Control):
	# Remove from tracking array
	var index = radar_pulses.find(pulse)
	if index != -1:
		radar_pulses.remove_at(index)

func _process(delta):
	# Update crosshair positions in case panel size changes
	update_crosshair_positions()

	# Handle movement to selected resource
	if is_moving_to_resource and selected_resource_node:
		handle_movement_to_resource(delta)

# Custom RadarPulse inner class
class RadarPulse extends Control:
	signal pulse_finished

	var growth_rate: float
	var visible_duration: float
	var fade_duration: float
	var outline_width: float
	var current_radius: float = 0.0
	var life_timer: float = 0.0
	var is_fading: bool = false

	func setup(p_growth_rate: float, p_visible_duration: float, p_fade_duration: float, p_outline_width: float):
		growth_rate = p_growth_rate
		visible_duration = p_visible_duration
		fade_duration = p_fade_duration
		outline_width = p_outline_width

		# Set initial properties
		modulate = Color.WHITE

	func _ready():
		# Ensure the pulse draws itself
		custom_minimum_size = Vector2(1000, 1000) # Large enough for any reasonable radar

	func _process(delta):
		life_timer += delta

		# Grow the radius
		current_radius += growth_rate * delta

		# Check if we should start fading
		if life_timer >= visible_duration and not is_fading:
			is_fading = true
			start_fade_out()

		# Force redraw
		queue_redraw()

	func _draw():
		# Draw circle outline with transparent fill
		if current_radius > 0:
			draw_arc(Vector2.ZERO, current_radius, 0, TAU, 64, Color.WHITE, outline_width, true)

	func start_fade_out():
		# Create tween for fade out
		var tween = create_tween()
		tween.tween_property(self, "modulate:a", 0.0, fade_duration)
		tween.tween_callback(finish_pulse)

	func finish_pulse():
		pulse_finished.emit()
		queue_free()

# Resource node management functions
func clear_resource_nodes():
	## Remove all existing resource nodes and pending reveal timers
	# Stop any ongoing movement
	stop_movement()
	selected_resource_node = null

	# Clean up existing resource nodes
	for node in active_resource_nodes:
		if is_instance_valid(node):
			node.force_cleanup()
	active_resource_nodes.clear()

	# Clean up any pending reveal timers
	for timer in active_reveal_timers:
		if is_instance_valid(timer):
			timer.stop()
			timer.queue_free()
	active_reveal_timers.clear()

func generate_resource_nodes():
	## Generate random resource nodes within the scanner UI
	var panel_size = scanner_ui.size
	var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)

	# Calculate safe placement bounds (leave some margin from edges)
	var margin = 30.0  # pixels from edge
	var max_x_offset = (panel_size.x / 2.0) - margin
	var max_y_offset = (panel_size.y / 2.0) - margin

	# Generate random number of resources
	var num_resources = randi_range(min_resources_per_scan, max_resources_per_scan)

	for i in range(num_resources):
		# Create new resource node
		var resource_node = area_node_scene.instantiate()

		# Generate random position relative to center
		var x_offset = randf_range(-max_x_offset, max_x_offset)
		var y_offset = randf_range(-max_y_offset, max_y_offset)
		var node_position = center + Vector2(x_offset, y_offset)

		# Calculate distance from center to this position
		var distance_from_center = Vector2(x_offset, y_offset).length()

		# Calculate time for radar wave to reach this position
		# scan_wave_growth_rate is pixels per second, so time = distance / speed
		var reveal_time = distance_from_center / scan_wave_growth_rate

		# Set position
		resource_node.position = node_position

		# Generate random resource type
		var resource_types = [0, 1, 2]  # FUEL, FOOD, PEOPLE
		var random_type = resource_types[randi() % resource_types.size()]

		# Add to scanner UI (but hidden initially)
		scanner_ui.add_child(resource_node)

		# Initialize the resource node
		resource_node.initialize_resource(random_type)

		# Hide the node initially
		resource_node.visible = false

		# Create timer to reveal the node when radar wave reaches it
		var reveal_timer = Timer.new()
		reveal_timer.wait_time = reveal_time
		reveal_timer.one_shot = true
		reveal_timer.timeout.connect(_on_resource_reveal_timer_timeout.bind(resource_node, reveal_timer))
		add_child(reveal_timer)
		reveal_timer.start()

		# Track the timer for cleanup
		active_reveal_timers.append(reveal_timer)

		# Track the node
		active_resource_nodes.append(resource_node)

		# Connect cleanup signal for when node expires naturally
		resource_node.tree_exiting.connect(_on_resource_node_removed.bind(resource_node))

func _on_resource_node_removed(node: Control):
	## Remove node from tracking when it's cleaned up
	var index = active_resource_nodes.find(node)
	if index != -1:
		active_resource_nodes.remove_at(index)

func _on_resource_reveal_timer_timeout(resource_node: Control, timer: Timer):
	## Reveal a resource node when the radar wave reaches it
	if is_instance_valid(resource_node):
		resource_node.visible = true
		# Optional: Add a small "pop" effect when revealed
		resource_node.scale = Vector2(0.8, 0.8)
		var tween = create_tween()
		tween.tween_property(resource_node, "scale", Vector2(1.0, 1.0), 0.2)
		tween.tween_property(resource_node, "scale", Vector2(1.0, 1.0), 0.1)

	# Remove timer from tracking
	var timer_index = active_reveal_timers.find(timer)
	if timer_index != -1:
		active_reveal_timers.remove_at(timer_index)

	# Clean up the timer
	timer.queue_free()

# Resource selection and movement functions
func _on_resource_node_selected(node: Control):
	## Handle when a resource node is selected
	# Deselect previous node if any
	if selected_resource_node and is_instance_valid(selected_resource_node):
		selected_resource_node.set_selected(false)

	# Select the new node
	selected_resource_node = node
	node.set_selected(true)

	# Calculate movement to this node
	start_movement_to_resource(node)

func start_movement_to_resource(target_node: Control):
	## Start moving towards the selected resource
	var panel_size = scanner_ui.size
	var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)
	var target_position = target_node.position

	# Calculate direction and distance
	var direction_to_target = target_position - center
	movement_distance_remaining = direction_to_target.length()

	if movement_distance_remaining > 0:
		movement_direction = direction_to_target.normalized()
		is_moving_to_resource = true
		print("Starting movement to resource. Distance: ", movement_distance_remaining, " pixels")

func handle_movement_to_resource(delta: float):
	"""Handle the movement simulation each frame"""
	if not is_instance_valid(selected_resource_node):
		stop_movement()
		return

	var movement_speed = PlayerStats.ship_speed
	var movement_this_frame = movement_speed * delta

	# Check if we've reached the target
	if movement_distance_remaining <= movement_this_frame:
		# We've arrived!
		complete_movement_to_resource()
		return

	# Move all resource nodes in the opposite direction to simulate ship movement
	var movement_vector = -movement_direction * movement_this_frame

	for node in active_resource_nodes:
		if is_instance_valid(node):
			node.position += movement_vector

	# Update remaining distance
	movement_distance_remaining -= movement_this_frame

func complete_movement_to_resource():
	"""Called when we reach the selected resource"""
	print("Reached resource!")
	stop_movement()

	# Center the selected resource exactly
	if is_instance_valid(selected_resource_node):
		var panel_size = scanner_ui.size
		var center = Vector2(panel_size.x / 2.0, panel_size.y / 2.0)
		selected_resource_node.position = center

	# Call placeholder function for resource collection
	handle_resource_collection()

func stop_movement():
	"""Stop the movement simulation"""
	is_moving_to_resource = false
	movement_direction = Vector2.ZERO
	movement_distance_remaining = 0.0

func handle_resource_collection():
	"""Placeholder function for when we reach a resource"""
	# TODO: Implement resource collection logic
	# This will be called when the ship reaches the selected resource
	# Ideas for implementation:
	# - Update UI elements
	# - Give resources to player based on resource type
	# - Trigger collection animations
	# - Emit signals for other systems
	# - Remove the collected resource node

	print("TODO: Implement resource collection logic")

	if is_instance_valid(selected_resource_node):
		var definition = selected_resource_node.resource_definitions[selected_resource_node.resource_type]
		print("Would collect: ", definition.name)
		# selected_resource_node.queue_free()  # Uncomment when ready to remove
		selected_resource_node = null
