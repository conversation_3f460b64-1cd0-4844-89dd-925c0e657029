extends Node

# This is the script for managing the auto save and load functionality for the game

# Save file configuration
const SAVE_FILE_PATH = "user://savegame.json"
var auto_save_interval: float = 30.0 # Auto save every 30 seconds (configurable)

# Auto save timer
var auto_save_timer: Timer

func _ready():
	setup_auto_save()
	load_game_on_startup()

func setup_auto_save():
	# Create and configure auto save timer
	auto_save_timer = Timer.new()
	auto_save_timer.wait_time = auto_save_interval
	auto_save_timer.timeout.connect(_on_auto_save_timeout)
	auto_save_timer.autostart = true
	add_child(auto_save_timer)

func _on_auto_save_timeout():
	save_game()
	print("Auto-saved game at: ", GameMechanics.get_military_time_string(), " Day ", GameMechanics.day_number)

func load_game_on_startup():
	# Check if save file exists on startup
	if FileAccess.file_exists(SAVE_FILE_PATH):
		print("Save file found, loading game...")
		load_game()
	else:
		print("No save file found, starting new game with defaults")
		# GameMechanics already initializes with defaults in _ready()

# Manual save function (can be called by buttons/UI)
func save_game() -> bool:
	var save_data = {
		"day_number": GameMechanics.day_number,
		"game_time_minutes": GameMechanics.game_time_minutes,
		"save_timestamp": Time.get_unix_time_from_system(),
		"player_stats": {
			"scan_cooldown": PlayerStats.scan_cooldown,
			"max_health": PlayerStats.max_health,
			"current_health": PlayerStats.current_health,
			"max_food": PlayerStats.max_food,
			"current_food": PlayerStats.current_food,
			"food_daily_consumption": PlayerStats.food_daily_consumption,
			"max_fuel_capacity": PlayerStats.max_fuel_capacity,
			"current_fuel": PlayerStats.current_fuel,
			"fuel_consumption_rate": PlayerStats.fuel_consumption_rate,
			"max_speed": PlayerStats.max_speed,
			"acceleration": PlayerStats.acceleration,
			"currency": PlayerStats.currency,
			"research_points": PlayerStats.research_points,
			"scanner_range": PlayerStats.scanner_range,
			"scanner_accuracy": PlayerStats.scanner_accuracy,
			"human_population": PlayerStats.human_population,
			"followers": PlayerStats.followers,
			"ship_human_capacity": PlayerStats.ship_human_capacity
		}
	}

	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.WRITE)
	if file == null:
		print("Error: Could not open save file for writing")
		SignalBus.emit_save_failed("Could not open save file for writing")
		return false

	var json_string = JSON.stringify(save_data)
	file.store_string(json_string)
	file.close()

	print("Game saved successfully - Day ", save_data.day_number, " at ", GameMechanics.get_military_time_string())
	print("Saved food: ", PlayerStats.current_food, "/", PlayerStats.max_food)
	print("Saved invasion progress - Population: ", PlayerStats.format_population_number(PlayerStats.human_population), " Followers: ", PlayerStats.format_population_number(PlayerStats.followers))

	# Emit save signal through SignalBus
	SignalBus.emit_game_saved()

	return true

# Manual load function (can be called by buttons/UI)
func load_game() -> bool:
	if not FileAccess.file_exists(SAVE_FILE_PATH):
		print("Error: Save file does not exist")
		SignalBus.emit_load_failed("Save file does not exist")
		return false

	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
	if file == null:
		print("Error: Could not open save file for reading")
		SignalBus.emit_load_failed("Could not open save file for reading")
		return false

	var json_string = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_string)

	if parse_result != OK:
		print("Error: Could not parse save file JSON")
		SignalBus.emit_load_failed("Could not parse save file JSON")
		return false

	var save_data = json.data

	# Validate save data has required fields
	if not save_data.has("day_number") or not save_data.has("game_time_minutes"):
		print("Error: Save file is missing required data")
		SignalBus.emit_load_failed("Save file is missing required data")
		return false

	# Load data into GameMechanics
	GameMechanics.day_number = save_data.day_number
	GameMechanics.game_time_minutes = save_data.game_time_minutes

	# Load PlayerStats if available (for backwards compatibility)
	if save_data.has("player_stats"):
		var player_data = save_data.player_stats
		PlayerStats.scan_cooldown = player_data.get("scan_cooldown", 5.0)
		PlayerStats.max_health = player_data.get("max_health", 100.0)
		PlayerStats.current_health = player_data.get("current_health", 100.0)
		PlayerStats.max_food = player_data.get("max_food", 100.0)
		PlayerStats.current_food = player_data.get("current_food", 80.0)
		# Handle both old and new food consumption formats for backward compatibility
		if player_data.has("food_daily_consumption"):
			PlayerStats.food_daily_consumption = player_data.get("food_daily_consumption", 10.0)
		else:
			# Convert old food_consumption_rate to new daily format (legacy support)
			var old_rate = player_data.get("food_consumption_rate", 2.0)
			# Old rate was per-10-seconds, convert to per-second, then to daily
			# old_rate / 10.0 = food per second
			# (old_rate / 10.0) * 144 = food per game day (144 seconds = 1 game day)
			var food_per_second = old_rate / 10.0
			var game_day_duration = 1440.0 / GameMechanics.game_minutes_per_real_second  # 144 seconds
			PlayerStats.food_daily_consumption = food_per_second * game_day_duration
		PlayerStats.max_fuel_capacity = player_data.get("max_fuel_capacity", 1000.0)
		PlayerStats.current_fuel = player_data.get("current_fuel", 1000.0)
		PlayerStats.fuel_consumption_rate = player_data.get("fuel_consumption_rate", 1.0)
		PlayerStats.max_speed = player_data.get("max_speed", 100.0)
		PlayerStats.acceleration = player_data.get("acceleration", 50.0)
		PlayerStats.currency = player_data.get("currency", 0)
		PlayerStats.research_points = player_data.get("research_points", 0)
		PlayerStats.scanner_range = player_data.get("scanner_range", 500.0)
		PlayerStats.scanner_accuracy = player_data.get("scanner_accuracy", 0.8)
		PlayerStats.human_population = player_data.get("human_population", 8200000000)
		PlayerStats.followers = player_data.get("followers", 0)
		PlayerStats.ship_human_capacity = player_data.get("ship_human_capacity", 5)
		print("PlayerStats loaded successfully")
		print("Loaded food: ", PlayerStats.current_food, "/", PlayerStats.max_food)
		print("Loaded invasion progress - Population: ", PlayerStats.format_population_number(PlayerStats.human_population), " Followers: ", PlayerStats.format_population_number(PlayerStats.followers))
		# Emit signal so UI updates with loaded values
		SignalBus.emit_player_food_changed(PlayerStats.current_food, PlayerStats.max_food)
	else:
		print("No PlayerStats found in save file, using defaults")

	print("Game loaded successfully - Day ", save_data.day_number, " at ", GameMechanics.get_military_time_string())

	# Emit load signal through SignalBus
	SignalBus.emit_game_loaded()

	return true

# Utility functions for managing auto-save
func set_auto_save_interval(seconds: float):
	auto_save_interval = seconds
	if auto_save_timer:
		auto_save_timer.wait_time = seconds

func pause_auto_save():
	if auto_save_timer:
		auto_save_timer.paused = true

func resume_auto_save():
	if auto_save_timer:
		auto_save_timer.paused = false

func get_save_file_info() -> Dictionary:
	if not FileAccess.file_exists(SAVE_FILE_PATH):
		return {}

	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
	if file == null:
		return {}

	var json_string = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_string)

	if parse_result != OK:
		return {}

	return json.data

func delete_save_file() -> bool:
	if FileAccess.file_exists(SAVE_FILE_PATH):
		var dir = DirAccess.open("user://")
		if dir:
			dir.remove("savegame.json")
			print("Save file deleted")
			return true
	return false
