extends Control
# Background panel for visual effects
@onready var background_panel: Panel = $BackgroundPanel

# Crash sequence variables
var crash_overlay_panel: Panel = null
var crash_tween: Tween = null
var is_crash_sequence_active: bool = false
var current_death_cause: String = ""

# Notification system variables
@onready var notification_container: VBoxContainer = %notificationVBox
var active_notifications: Array[Control] = []

# Notification settings
const NOTIFICATION_WIDTH = 300
const NOTIFICATION_HEIGHT = 50
const APPEAR_DURATION = 0.25
const DISPLAY_DURATION = 10.0
const FADE_DURATION = 2.0
const NOTIFICATION_SPACING = 10

func _ready():
	setup_notification_system()
	connect_signals()

func setup_notification_system():
	# VBox should already exist in scene with "right wide" anchor preset
	# Just configure spacing between notifications
	notification_container.add_theme_constant_override("separation", NOTIFICATION_SPACING)

func connect_signals():
	# Connect to SignalBus for game events
	SignalBus.game_saved.connect(_on_game_saved)
	SignalBus.crash_sequence_started.connect(_on_crash_sequence_started)
	# Future: Add more signal connections here
	# SignalBus.game_loaded.connect(_on_game_loaded)
	# SignalBus.notification_requested.connect(_on_notification_requested)

# Signal handlers
func _on_game_saved():
	show_notification("res://assets/icons/saving.png", "Auto Save - " + "Day " + str(GameMechanics.day_number) + " : " + str(GameMechanics.get_military_time_string()))

# Core notification system
func show_notification(icon_path: String, text: String):
	var notif_node = create_notification(icon_path, text)

	# Add to container (new notifications go to the bottom)
	notification_container.add_child(notif_node)
	active_notifications.append(notif_node)

	# Instantly move container down by notification height to hide the new addition
	notification_container.position.y += NOTIFICATION_HEIGHT + NOTIFICATION_SPACING

	# Animate container back up to reveal the new notification
	animate_container_up()

	# Start the notification lifecycle (display timer)
	start_notification_lifecycle(notif_node)

func create_notification(icon_path: String, text: String) -> Control:
	# Create main container
	var notif_node = HBoxContainer.new()
	notif_node.custom_minimum_size = Vector2(NOTIFICATION_WIDTH, NOTIFICATION_HEIGHT)
	notif_node.size_flags_horizontal = Control.SIZE_SHRINK_END

	# Create background panel for styling
	var background = Panel.new()
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	notif_node.add_child(background)


	# Create label
	var label = Label.new()
	label.text = text
	label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	notif_node.add_child(label)

	# Create icon
	var icon = TextureRect.new()
	icon.custom_minimum_size = Vector2(50, 50)
	icon.expand_mode = TextureRect.EXPAND_IGNORE_SIZE
	icon.texture = load(icon_path)
	notif_node.add_child(icon)

	# Notification starts fully visible
	return notif_node

# Simplified animation functions
func animate_container_up():
	# Animate the entire container back up to reveal the new notification
	var tween = create_tween()
	tween.tween_property(notification_container, "position:y", notification_container.position.y - (NOTIFICATION_HEIGHT + NOTIFICATION_SPACING), APPEAR_DURATION).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)

func start_notification_lifecycle(notif_node: Control):
	# Wait for appear duration + display duration, then start fade out
	var timer = get_tree().create_timer(APPEAR_DURATION + DISPLAY_DURATION)
	timer.timeout.connect(start_notification_fadeout.bind(notif_node))

func start_notification_fadeout(notif_node: Control):
	# Create tween for fade out
	var tween = create_tween()

	# Fade out
	tween.tween_property(notif_node, "modulate:a", 0.0, FADE_DURATION).set_ease(Tween.EASE_IN)

	# Remove notification after fade out
	tween.tween_callback(remove_notification.bind(notif_node))

func remove_notification(notif_node: Control):
	# Remove from active notifications array
	var index = active_notifications.find(notif_node)
	if index != -1:
		active_notifications.remove_at(index)

	# Remove from scene
	notif_node.queue_free()

# Crash sequence functions
func _on_crash_sequence_started(death_cause: String):
	## Handle crash sequence UI when player dies
	if is_crash_sequence_active:
		return # Prevent multiple crash sequences

	is_crash_sequence_active = true
	current_death_cause = death_cause
	print("MainScene: Starting crash sequence UI for death cause: ", death_cause)

	# Start red pulsing background
	start_crash_background_effect()

	# Create and show crash overlay
	create_crash_overlay(death_cause)

func start_crash_background_effect():
	## Start red pulsing background effect - primarily red with lighter pulsing
	# Stop any existing tween
	if crash_tween:
		crash_tween.kill()

	# Define colors: dark red base with lighter red pulse
	var dark_red = Color(0.5, 0.1, 0.1, 1.0)   # Dark red base
	var light_red = Color(0.8, 0.3, 0.3, 1.0)  # Lighter red for pulse

	# Set initial dark red background
	var initial_style = StyleBoxFlat.new()
	initial_style.bg_color = dark_red
	background_panel.add_theme_stylebox_override("panel", initial_style)

	# Create breathing pulse effect
	crash_tween = create_tween()
	crash_tween.set_loops()  # Infinite loop

	# Pulse from dark red to light red
	crash_tween.tween_method(
		func(progress: float):
			var current_style = StyleBoxFlat.new()
			current_style.bg_color = dark_red.lerp(light_red, progress)
			background_panel.add_theme_stylebox_override("panel", current_style),
		0.0, 1.0, 1.5
	).set_ease(Tween.EASE_IN_OUT)

	# Pulse from light red back to dark red
	crash_tween.tween_method(
		func(progress: float):
			var current_style = StyleBoxFlat.new()
			current_style.bg_color = light_red.lerp(dark_red, progress)
			background_panel.add_theme_stylebox_override("panel", current_style),
		0.0, 1.0, 1.5
	).set_ease(Tween.EASE_IN_OUT)

func create_crash_overlay(death_cause: String):
	## Create the crash overlay panel with death message and respawn button
	# Create main overlay panel
	crash_overlay_panel = Panel.new()
	crash_overlay_panel.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)

	# Create semi-transparent black StyleBoxFlat for overlay
	var overlay_style = StyleBoxFlat.new()
	overlay_style.bg_color = Color(0.0, 0.0, 0.0, 0.7)  # Semi-transparent black
	crash_overlay_panel.add_theme_stylebox_override("panel", overlay_style)

	add_child(crash_overlay_panel)

	# Create content container
	var content_container = VBoxContainer.new()
	content_container.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	content_container.custom_minimum_size = Vector2(600, 400)
	content_container.position = Vector2(-300, -200)  # Center it
	crash_overlay_panel.add_child(content_container)

	# Add spacing
	content_container.add_theme_constant_override("separation", 30)

	# Create main message label
	var main_label = Label.new()
	main_label.text = "You failed to take over Earth with this body,\nbut how about another?"
	main_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	main_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	main_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	main_label.add_theme_font_size_override("font_size", 24)
	content_container.add_child(main_label)

	# Create death reason label
	var death_reason_label = Label.new()
	var death_text = get_death_reason_text(death_cause)
	death_reason_label.text = death_text
	death_reason_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	death_reason_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	death_reason_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	death_reason_label.add_theme_font_size_override("font_size", 16)
	death_reason_label.add_theme_color_override("font_color", Color.LIGHT_GRAY)
	content_container.add_child(death_reason_label)

	# Create respawn button
	var respawn_button = Button.new()
	respawn_button.text = "Download Consciousness"
	respawn_button.custom_minimum_size = Vector2(300, 60)
	respawn_button.add_theme_font_size_override("font_size", 18)
	respawn_button.pressed.connect(_on_respawn_button_pressed)

	# Center the button
	var button_container = HBoxContainer.new()
	button_container.alignment = BoxContainer.ALIGNMENT_CENTER
	button_container.add_child(respawn_button)
	content_container.add_child(button_container)

func get_death_reason_text(death_cause: String) -> String:
	## Get descriptive text for the death cause
	match death_cause:
		"food":
			return "Cause of death: Starvation\nYour ship ran out of food supplies."
		"fuel":
			return "Cause of death: Fuel Depletion\nYour ship ran out of fuel and crashed."
		_:
			return "Cause of death: Unknown\nSomething went wrong with your ship."

func _on_respawn_button_pressed():
	## Handle respawn button press - trigger consciousness download and clean up UI
	print("MainScene: Respawn button pressed, downloading consciousness...")

	# Trigger consciousness download in GameMechanics (this handles resource reset and game resume)
	GameMechanics.handle_consciousness_download(current_death_cause)

	# Clean up crash overlay
	if crash_overlay_panel:
		crash_overlay_panel.queue_free()
		crash_overlay_panel = null

	# Stop crash background effect and restore normal background
	if crash_tween:
		crash_tween.kill()
		crash_tween = null

	# Restore normal background color
	var normal_style = StyleBoxFlat.new()
	normal_style.bg_color = Color(0.0, 0.0, 0.0, 1.0)  # Black
	background_panel.add_theme_stylebox_override("panel", normal_style)

	# Reset crash sequence state
	is_crash_sequence_active = false
	current_death_cause = ""

	# Emit signal that crash sequence is complete
	SignalBus.emit_crash_sequence_completed()

	print("MainScene: Crash sequence UI cleaned up, new life started")
