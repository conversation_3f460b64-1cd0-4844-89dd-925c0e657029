extends Control

# Notification system variables
@onready var notification_container: VBoxContainer = %notificationVBox
var active_notifications: Array[Control] = []

# Notification settings
const NOTIFICATION_WIDTH = 300
const NOTIFICATION_HEIGHT = 50
const APPEAR_DURATION = 0.25
const DISPLAY_DURATION = 10.0
const FADE_DURATION = 2.0
const NOTIFICATION_SPACING = 10

func _ready():
	setup_notification_system()
	connect_signals()

func setup_notification_system():
	# VBox should already exist in scene with "right wide" anchor preset
	# Just configure spacing between notifications
	notification_container.add_theme_constant_override("separation", NOTIFICATION_SPACING)

func connect_signals():
	# Connect to SignalBus for game events
	SignalBus.game_saved.connect(_on_game_saved)
	# Future: Add more signal connections here
	# SignalBus.game_loaded.connect(_on_game_loaded)
	# SignalBus.notification_requested.connect(_on_notification_requested)

# Signal handlers
func _on_game_saved():
	show_notification("res://assets/icons/saving.png", "Auto Save - " + "Day " + str(GameMechanics.day_number) + " : " + str(GameMechanics.get_military_time_string()))

# Core notification system
func show_notification(icon_path: String, text: String):
	var notif_node = create_notification(icon_path, text)

	# Add to container (new notifications go to the bottom)
	notification_container.add_child(notif_node)
	active_notifications.append(notif_node)

	# Instantly move container down by notification height to hide the new addition
	notification_container.position.y += NOTIFICATION_HEIGHT + NOTIFICATION_SPACING

	# Animate container back up to reveal the new notification
	animate_container_up()

	# Start the notification lifecycle (display timer)
	start_notification_lifecycle(notif_node)

func create_notification(icon_path: String, text: String) -> Control:
	# Create main container
	var notif_node = HBoxContainer.new()
	notif_node.custom_minimum_size = Vector2(NOTIFICATION_WIDTH, NOTIFICATION_HEIGHT)
	notif_node.size_flags_horizontal = Control.SIZE_SHRINK_END

	# Create background panel for styling
	var background = Panel.new()
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	notif_node.add_child(background)


	# Create label
	var label = Label.new()
	label.text = text
	label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	notif_node.add_child(label)

	# Create icon
	var icon = TextureRect.new()
	icon.custom_minimum_size = Vector2(50, 50)
	icon.expand_mode = TextureRect.EXPAND_IGNORE_SIZE
	icon.texture = load(icon_path)
	notif_node.add_child(icon)

	# Notification starts fully visible
	return notif_node

# Simplified animation functions
func animate_container_up():
	# Animate the entire container back up to reveal the new notification
	var tween = create_tween()
	tween.tween_property(notification_container, "position:y", notification_container.position.y - (NOTIFICATION_HEIGHT + NOTIFICATION_SPACING), APPEAR_DURATION).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)

func start_notification_lifecycle(notif_node: Control):
	# Wait for appear duration + display duration, then start fade out
	var timer = get_tree().create_timer(APPEAR_DURATION + DISPLAY_DURATION)
	timer.timeout.connect(start_notification_fadeout.bind(notif_node))

func start_notification_fadeout(notif_node: Control):
	# Create tween for fade out
	var tween = create_tween()

	# Fade out
	tween.tween_property(notif_node, "modulate:a", 0.0, FADE_DURATION).set_ease(Tween.EASE_IN)

	# Remove notification after fade out
	tween.tween_callback(remove_notification.bind(notif_node))

func remove_notification(notif_node: Control):
	# Remove from active notifications array
	var index = active_notifications.find(notif_node)
	if index != -1:
		active_notifications.remove_at(index)

	# Remove from scene
	notif_node.queue_free()
