extends Node

# Game time variables
var game_time_minutes: int = 0 # Total minutes since start of day (0-1439)
var day_number: int = 1
var is_paused: bool = false

## Get current time as string
#var current_time = get_military_time_string() # Returns "14:30"

## Change game speed (e.g., make it faster for testing)
#set_game_speed(8.0) # 8 game minutes per real second

## Set specific time
#set_time(14, 30) # Set to 14:30

# Time progression settings
var game_minutes_per_real_second: float = 10.0 # Configurable speed multiplier
var time_accumulator: float = 0.0

# Note: Signals are now handled through SignalBus for centralized management

func _ready():
	game_time_minutes = 0 # Start at 00:00
	day_number = 1
	is_paused = false

func _process(delta: float):
	if not is_paused:
		advance_time(delta)

func advance_time(delta: float):
	# Accumulate time based on the game speed multiplier
	time_accumulator += delta * game_minutes_per_real_second

	# Convert accumulated time to whole minutes
	var minutes_to_add = int(time_accumulator)
	if minutes_to_add > 0:
		time_accumulator -= minutes_to_add
		add_game_minutes(minutes_to_add)

func add_game_minutes(minutes: int):
	var old_hours = get_current_hours()
	var old_minutes = get_current_minutes()

	game_time_minutes += minutes

	# Check for day rollover (24:00 = 1440 minutes)
	if game_time_minutes >= 1440:
		var days_passed = game_time_minutes / 1440
		day_number += days_passed
		game_time_minutes = game_time_minutes % 1440
		SignalBus.emit_day_changed(day_number)

	# Emit time change signal if time actually changed
	var new_hours = get_current_hours()
	var new_minutes = get_current_minutes()
	if old_hours != new_hours or old_minutes != new_minutes:
		SignalBus.emit_time_changed(new_hours, new_minutes)

# Utility functions for getting time components
func get_current_hours() -> int:
	return game_time_minutes / 60

func get_current_minutes() -> int:
	return game_time_minutes % 60

func get_military_time_string() -> String:
	var hours = get_current_hours()
	var minutes = get_current_minutes()
	return "%02d:%02d" % [hours, minutes]

# Control functions
func pause_time():
	is_paused = true
	SignalBus.emit_game_paused()

func resume_time():
	is_paused = false
	SignalBus.emit_game_resumed()

func toggle_pause():
	is_paused = !is_paused
	if is_paused:
		SignalBus.emit_game_paused()
	else:
		SignalBus.emit_game_resumed()

func set_game_speed(minutes_per_second: float):
	game_minutes_per_real_second = minutes_per_second

func set_time(hours: int, minutes: int):
	game_time_minutes = (hours * 60) + minutes
	SignalBus.emit_time_changed(hours, minutes)
