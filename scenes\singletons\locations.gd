extends Node


## In this autoload I want any file to have access to what can be done at locations

## Realistically, only the AreaScan and the Simulation need to be aware of this I think

## For each location there are a number of things that could be an option for the player to do but at least 1


## Fuel Options - Wood Icon
## Options in priority order on liklihood to appear
# Gather Wood - adds some fuel directly to playerstats current_fuel (this should almost always be an option)

# Gather Berries - adds a small amount of food to the playerstats current_food

# Hunt Squirrels - adds a small amount of food

# Salvage Fuel from Car - adds a moderate amount of fuel directly to the playerstats current_fuel

# Hunt Deer - adds a moderate amount of food 

## Food Options - Food Icon
## Options in priority order on liklihood to appear (multiple can appear)
# Gather berries - adds a small amount of food to playerstats current_food

# Hunt Squirrels - adds a small amount of food

# Raid Picnic - adds a moderate amount of food

# Salvage Fuel from Car - Adds a moderate amount of fuel directly to the playerstats current_fuel

# Hunt Deer - adds a moderate amount of food

# Convert Humans - adds a few 'people' to the devotees list

## People Options - Person Icon
## Options in priority order on liklihood to appear (multiple can appear)
# This will need to be built out once we add the 'human' resource type
