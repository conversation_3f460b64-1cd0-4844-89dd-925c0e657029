extends Node

## Location Options Autoload
## Provides centralized access to location interaction data and logic
## Used by AreaScan and Simulation for location-based events

# Resource type constants (matching area_node.gd)
enum ResourceType {
	FUEL = 0,
	FOOD = 1,
	PEOPLE = 2,
	CROPCIRCLE = 3
}

# Location option definitions with probabilities and rewards
var location_options_data = {
	ResourceType.FUEL: {  # Fuel locations (Wood icon)
		"name": "⛽ Fuel Location",
		"common": [
			{"action": "Gather Wood", "resource": "fuel", "min": 8, "max": 15, "weight": 80},
			{"action": "Gather Berries", "resource": "food", "min": 3, "max": 8, "weight": 60},
			{"action": "Hunt Squirrels", "resource": "food", "min": 4, "max": 10, "weight": 40}
		],
		"uncommon": [
			{"action": "Salvage Fuel from Car", "resource": "fuel", "min": 15, "max": 25, "weight": 25},
			{"action": "Hunt Deer", "resource": "food", "min": 12, "max": 20, "weight": 20}
		]
	},
	ResourceType.FOOD: {  # Food locations (Food icon)
		"name": "🍎 Food Location",
		"common": [
			{"action": "Gather Berries", "resource": "food", "min": 5, "max": 12, "weight": 80},
			{"action": "Hunt Squirrels", "resource": "food", "min": 6, "max": 14, "weight": 60},
			{"action": "Raid Picnic", "resource": "food", "min": 10, "max": 18, "weight": 50}
		],
		"uncommon": [
			{"action": "Salvage Fuel from Car", "resource": "fuel", "min": 12, "max": 20, "weight": 30},
			{"action": "Hunt Deer", "resource": "food", "min": 15, "max": 25, "weight": 25},
			{"action": "Convert Humans", "resource": "followers", "min": 2, "max": 5, "weight": 15}
		]
	},
	ResourceType.PEOPLE: {  # People locations (Person icon)
		"name": "👥 People Location",
		"common": [
			{"action": "Observe Humans", "resource": "food", "min": 2, "max": 6, "weight": 70},
			{"action": "Scavenge Supplies", "resource": "fuel", "min": 4, "max": 10, "weight": 60}
		],
		"uncommon": [
			{"action": "Convert Humans", "resource": "followers", "min": 3, "max": 8, "weight": 40},
			{"action": "Abduct Humans", "resource": "abducted", "min": 1, "max": 3, "weight": 25}
		]
	},
	ResourceType.CROPCIRCLE: {  # Crop circle locations (Wheat icon)
		"name": "🛸 Crop Circle",
		"common": [
			{"action": "Investigate Circle", "resource": "research", "min": 5, "max": 12, "weight": 80},
			{"action": "Gather Crops", "resource": "food", "min": 8, "max": 15, "weight": 60}
		],
		"uncommon": [
			{"action": "Commune with Aliens", "resource": "followers", "min": 5, "max": 10, "weight": 30},
			{"action": "Harvest Energy", "resource": "fuel", "min": 20, "max": 35, "weight": 20}
		]
	}
}

# Probability settings
var uncommon_option_chance: float = 0.3  # 30% chance for uncommon options
var min_common_options: int = 1
var max_common_options: int = 2

func get_location_name(resource_type: int) -> String:
	## Get the display name for a location type
	if location_options_data.has(resource_type):
		return location_options_data[resource_type].name
	return "📍 Unknown Location"

func generate_location_options(resource_type: int) -> Array[Dictionary]:
	## Generate 2-4 random options based on the resource type
	if not location_options_data.has(resource_type):
		print("Locations: No options defined for resource type: ", resource_type)
		return []

	var options_data = location_options_data[resource_type]
	var selected_options: Array[Dictionary] = []

	# Get available option pools
	var common_options = options_data.get("common", [])
	var uncommon_options = options_data.get("uncommon", [])

	# Select 1-2 common options
	var num_common = randi_range(min_common_options, min(max_common_options, common_options.size()))
	for i in range(num_common):
		if common_options.size() > 0:
			var option = common_options[randi() % common_options.size()]
			if not selected_options.has(option):
				selected_options.append(option)

	# Maybe add an uncommon option
	if randf() < uncommon_option_chance and uncommon_options.size() > 0:
		var option = uncommon_options[randi() % uncommon_options.size()]
		if not selected_options.has(option):
			selected_options.append(option)

	print("Locations: Generated ", selected_options.size(), " options for resource type ", resource_type)
	return selected_options

func get_resource_display_name(resource_type: String) -> String:
	## Get display name for resource types
	match resource_type:
		"fuel": return "Fuel"
		"food": return "Food"
		"followers": return "Followers"
		"abducted": return "Humans"
		"research": return "Research"
		_: return "Resources"

func grant_resources_to_player(resource_type: String, amount: float) -> bool:
	## Grant resources to the player and return success status
	match resource_type:
		"fuel":
			PlayerStats.current_fuel = min(PlayerStats.max_fuel_capacity, PlayerStats.current_fuel + amount)
			SignalBus.emit_player_fuel_changed(PlayerStats.current_fuel, PlayerStats.max_fuel_capacity)
			print("Locations: Granted %.1f fuel. Current: %.1f/%.1f" % [amount, PlayerStats.current_fuel, PlayerStats.max_fuel_capacity])
			return true

		"food":
			PlayerStats.add_food(amount)
			print("Locations: Granted %.1f food. Current: %.1f/%.1f" % [amount, PlayerStats.current_food, PlayerStats.max_food])
			return true

		"followers":
			var actual_followers = PlayerStats.add_followers(int(amount))
			print("Locations: Converted %d humans to followers. Total followers: %s" % [actual_followers, PlayerStats.format_population_number(PlayerStats.followers)])
			return true

		"research":
			PlayerStats.research_points += int(amount)
			SignalBus.emit_player_research_changed(PlayerStats.research_points)
			print("Locations: Granted %d research points. Total: %d" % [int(amount), PlayerStats.research_points])
			return true

		"abducted":
			# TODO: Implement abduction system when ship capacity is ready
			print("Locations: Abducted %.0f humans (not yet implemented)" % amount)
			return false

		_:
			print("Locations: Unknown resource type: ", resource_type)
			return false

func create_option_button_text(option_data: Dictionary, amount: float) -> String:
	## Create formatted button text for an option
	var formatted_amount = "%.1f" % amount if amount != int(amount) else str(int(amount))
	var resource_name = get_resource_display_name(option_data.resource)
	return "%s %s %s" % [option_data.action, formatted_amount, resource_name]
