extends Control

@onready var fuel_bar: ProgressBar = %FuelBar
@onready var durability_bar: ProgressBar = %DurabilityBar
@onready var speed_bar: ProgressBar = %SpeedBar
@onready var fuel_amount_label: Label = %FuelAmountLabel

func _ready() -> void:
	# Initialize fuel bar with current values
	fuel_bar.max_value = PlayerStats.max_fuel_capacity
	fuel_bar.value = PlayerStats.current_fuel

	# Initialize speed bar
	speed_bar.max_value = PlayerStats.max_speed
	speed_bar.value = PlayerStats.ship_speed

	# Connect to SignalBus for real-time fuel updates
	SignalBus.player_fuel_changed.connect(_on_player_fuel_changed)

func _on_player_fuel_changed(current_fuel: float, max_fuel: float):
	## Update fuel bar when fuel values change
	fuel_bar.max_value = max_fuel
	fuel_bar.value = current_fuel
