extends Control

@onready var fuel_bar: ProgressBar = %FuelBar
@onready var durability_bar: ProgressBar = %DurabilityBar
@onready var speed_bar: ProgressBar = %SpeedBar
@onready var fuel_amount_label: Label = %FuelAmountLabel

func _ready() -> void:
	# Initialize fuel bar with current values
	fuel_bar.max_value = PlayerStats.max_fuel_capacity
	fuel_bar.value = PlayerStats.current_fuel

	# Initialize fuel amount label
	update_fuel_label(PlayerStats.current_fuel, PlayerStats.max_fuel_capacity)

	# Initialize speed bar
	speed_bar.max_value = PlayerStats.max_speed
	speed_bar.value = PlayerStats.ship_speed

	# Connect to SignalBus for real-time fuel updates
	SignalBus.player_fuel_changed.connect(_on_player_fuel_changed)

func _on_player_fuel_changed(current_fuel: float, max_fuel: float):
	## Update fuel bar and label when fuel values change
	fuel_bar.max_value = max_fuel
	fuel_bar.value = current_fuel
	update_fuel_label(current_fuel, max_fuel)

func update_fuel_label(current_fuel: float, max_fuel: float):
	## Update fuel amount label with [current / max] format using comma-separated whole numbers
	var current_formatted = format_number_with_commas(int(current_fuel))
	var max_formatted = format_number_with_commas(int(max_fuel))
	fuel_amount_label.text = "[%s / %s]" % [current_formatted, max_formatted]

func format_number_with_commas(number: int) -> String:
	## Format a number with comma separators (e.g., 1000 -> 1,000)
	var number_str = str(number)
	var result = ""
	var count = 0

	# Process digits from right to left
	for i in range(number_str.length() - 1, -1, -1):
		if count > 0 and count % 3 == 0:
			result = "," + result
		result = number_str[i] + result
		count += 1

	return result
