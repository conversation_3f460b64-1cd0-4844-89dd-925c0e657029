extends Control

@onready var food_bar: ProgressBar = %FoodBar

func _ready() -> void:
	# Initialize food bar with current values
	food_bar.max_value = PlayerStats.max_food
	food_bar.value = PlayerStats.current_food

	# Connect to SignalBus for real-time food updates
	SignalBus.player_food_changed.connect(_on_player_food_changed)

func _on_player_food_changed(current_food: float, max_food: float):
	"""Update food bar when food values change"""
	food_bar.max_value = max_food
	food_bar.value = current_food
