extends Control

@onready var food_bar: ProgressBar = %FoodBar
@onready var food_label: Label = %FoodLabel

func _ready() -> void:
	# Initialize food bar with current values
	food_bar.max_value = PlayerStats.max_food
	food_bar.value = PlayerStats.current_food

	# Initialize food label with current values
	update_food_label(PlayerStats.current_food, PlayerStats.max_food)

	# Connect to SignalBus for real-time food updates
	SignalBus.player_food_changed.connect(_on_player_food_changed)

func _on_player_food_changed(current_food: float, max_food: float):
	## Update food bar and label when food values change
	food_bar.max_value = max_food
	food_bar.value = current_food
	update_food_label(current_food, max_food)

func update_food_label(current_food: float, max_food: float):
	## Update food label with format: current_food (1 decimal) / max_food (whole number)
	food_label.text = "%.1f / %d" % [current_food, int(max_food)]
