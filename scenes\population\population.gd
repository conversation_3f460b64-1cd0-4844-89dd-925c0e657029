extends Control

## Simple Population & Followers Display
## Shows human population and followers across 10 digit labels each

@onready var _10: Label = %"10"  # Billions
@onready var _9: Label = %"9"   # Hundred millions
@onready var _8: Label = %"8"   # Ten millions
@onready var _7: Label = %"7"   # Millions
@onready var _6: Label = %"6"   # Hundred thousands
@onready var _5: Label = %"5"   # Ten thousands
@onready var _4: Label = %"4"   # Thousands
@onready var _3: Label = %"3"   # Hundreds
@onready var _2: Label = %"2"   # Tens
@onready var _1: Label = %"1"   # Ones

@onready var _10f: Label = %"10f"
@onready var _9f: Label = %"9f"
@onready var _8f: Label = %"8f"
@onready var _7f: Label = %"7f"
@onready var _6f: Label = %"6f"
@onready var _5f: Label = %"5f"
@onready var _4f: Label = %"4f"
@onready var _3f: Label = %"3f"
@onready var _2f: Label = %"2f"
@onready var _1f: Label = %"1f"


var digit_labels: Array[Label] = []
var follower_labels: Array[Label] = []

func _ready():
	## Initialize the population display system
	setup_digit_labels()
	connect_signals()
	update_population_display(PlayerStats.human_population)
	update_followers_display(PlayerStats.followers)

func setup_digit_labels():
	## Store labels in array for easy iteration (billions to ones)
	digit_labels = [_10, _9, _8, _7, _6, _5, _4, _3, _2, _1]
	follower_labels = [_10f, _9f, _8f, _7f, _6f, _5f, _4f, _3f, _2f, _1f]

func connect_signals():
	## Connect to population and follower change signals
	SignalBus.human_population_changed.connect(_on_population_changed)
	SignalBus.followers_changed.connect(_on_followers_changed)

func _on_population_changed(new_population: int):
	## Handle population changes and update display
	update_population_display(new_population)

func _on_followers_changed(new_followers: int):
	## Handle follower changes and update display
	update_followers_display(new_followers)

func update_population_display(population: int):
	## Update the population display immediately
	# Convert population to 10-digit string (pad with leading zeros)
	var population_string = str(population).pad_zeros(10)

	# Set each digit label
	for i in range(10):
		var digit = population_string[i]
		digit_labels[i].text = digit

func update_followers_display(followers: int):
	## Update the followers display immediately
	# Convert followers to 10-digit string (pad with leading zeros)
	var followers_string = str(followers).pad_zeros(10)

	# Set each follower digit label
	for i in range(10):
		var digit = followers_string[i]
		follower_labels[i].text = digit
