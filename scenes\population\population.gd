extends Control

## Simple Population Display
## Shows human population across 10 digit labels

@onready var _10: Label = %"10"  # Billions
@onready var _9: Label = %"9"   # Hundred millions
@onready var _8: Label = %"8"   # Ten millions
@onready var _7: Label = %"7"   # Millions
@onready var _6: Label = %"6"   # Hundred thousands
@onready var _5: Label = %"5"   # Ten thousands
@onready var _4: Label = %"4"   # Thousands
@onready var _3: Label = %"3"   # Hundreds
@onready var _2: Label = %"2"   # Tens
@onready var _1: Label = %"1"   # Ones

var digit_labels: Array[Label] = []

func _ready():
	## Initialize the population display system
	setup_digit_labels()
	connect_signals()
	update_population_display(PlayerStats.human_population)

func setup_digit_labels():
	## Store labels in array for easy iteration (billions to ones)
	digit_labels = [_10, _9, _8, _7, _6, _5, _4, _3, _2, _1]

func connect_signals():
	## Connect to population change signals
	SignalBus.human_population_changed.connect(_on_population_changed)

func _on_population_changed(new_population: int):
	## Handle population changes and update display
	update_population_display(new_population)

func update_population_display(population: int):
	## Update the population display immediately
	# Convert population to 10-digit string (pad with leading zeros)
	var population_string = str(population).pad_zeros(10)

	# Set each digit label
	for i in range(10):
		var digit = population_string[i]
		digit_labels[i].text = digit
