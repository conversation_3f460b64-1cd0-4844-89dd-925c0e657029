extends Control

## Solari Split-Flap Population Display
## Mimics classic flip clock animation for population numbers

@onready var _10: Label = %"10"  # Billions
@onready var _9: Label = %"9"   # Hundred millions
@onready var _8: Label = %"8"   # Ten millions
@onready var _7: Label = %"7"   # Millions
@onready var _6: Label = %"6"   # Hundred thousands
@onready var _5: Label = %"5"   # Ten thousands
@onready var _4: Label = %"4"   # Thousands
@onready var _3: Label = %"3"   # Hundreds
@onready var _2: Label = %"2"   # Tens
@onready var _1: Label = %"1"   # Ones

## Animation variables
var digit_labels: Array[Label] = []
var current_digits: Array[int] = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
var target_digits: Array[int] = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
var flip_tweens: Array[Tween] = []
const FLIP_DURATION = 0.3  # Duration for each flip animation
const FLIP_DELAY_BETWEEN_DIGITS = 0.05  # Stagger delay between digits

func _ready():
	## Initialize the population display system
	setup_digit_labels()
	connect_signals()
	update_population_display(PlayerStats.human_population)

func setup_digit_labels():
	## Store labels in array for easy iteration (billions to ones)
	digit_labels = [_10, _9, _8, _7, _6, _5, _4, _3, _2, _1]

	# Initialize tweens array
	flip_tweens.resize(10)
	for i in range(10):
		flip_tweens[i] = null

func connect_signals():
	## Connect to population change signals
	SignalBus.human_population_changed.connect(_on_population_changed)

func _on_population_changed(new_population: int):
	## Handle population changes and trigger flip animations
	update_population_display(new_population)

func update_population_display(population: int):
	## Update the population display with flip animations
	# Convert population to 10-digit array (pad with leading zeros)
	var population_string = str(population).pad_zeros(10)
	var new_digits: Array[int] = []

	for i in range(10):
		new_digits.append(int(population_string[i]))

	# Store target digits and animate changes
	target_digits = new_digits
	animate_digit_changes()

func animate_digit_changes():
	## Animate digits that have changed with staggered timing
	for i in range(10):
		if current_digits[i] != target_digits[i]:
			# Add delay based on position (rightmost digits flip first)
			var delay = (9 - i) * FLIP_DELAY_BETWEEN_DIGITS
			animate_single_digit(i, delay)

func animate_single_digit(digit_index: int, delay: float):
	## Animate a single digit with Solari split-flap effect
	var label = digit_labels[digit_index]
	var _old_digit = current_digits[digit_index]  # Store for potential future use
	var new_digit = target_digits[digit_index]

	# Kill existing tween for this digit
	if flip_tweens[digit_index]:
		flip_tweens[digit_index].kill()

	# Create new tween
	flip_tweens[digit_index] = create_tween()
	var tween = flip_tweens[digit_index]

	# Add initial delay
	if delay > 0:
		tween.tween_delay(delay)

	# Store original scale and position
	var original_scale = label.scale
	var original_position = label.position

	# Phase 1: Squish top half down (old digit disappearing)
	tween.tween_method(
		func(progress: float):
			# Squish from top, keeping bottom anchored
			label.scale.y = original_scale.y * (1.0 - progress)
			label.position.y = original_position.y + (original_scale.y * label.size.y * progress * 0.5),
		0.0, 1.0, FLIP_DURATION * 0.5
	).set_ease(Tween.EASE_IN)

	# Phase 2: Change the digit and expand bottom half up (new digit appearing)
	tween.tween_callback(func():
		label.text = str(new_digit)
		current_digits[digit_index] = new_digit
	)

	tween.tween_method(
		func(progress: float):
			# Expand from bottom, keeping bottom anchored
			label.scale.y = original_scale.y * progress
			label.position.y = original_position.y + (original_scale.y * label.size.y * (1.0 - progress) * 0.5),
		0.0, 1.0, FLIP_DURATION * 0.5
	).set_ease(Tween.EASE_OUT)

	# Ensure final state is correct
	tween.tween_callback(func():
		label.scale = original_scale
		label.position = original_position
		flip_tweens[digit_index] = null
	)

## Utility functions
func set_population_immediately(population: int):
	## Set population without animation (for initialization)
	var population_string = str(population).pad_zeros(10)

	for i in range(10):
		var digit = int(population_string[i])
		current_digits[i] = digit
		target_digits[i] = digit
		digit_labels[i].text = str(digit)

func stop_all_animations():
	## Stop all running flip animations
	for i in range(10):
		if flip_tweens[i]:
			flip_tweens[i].kill()
			flip_tweens[i] = null

func get_current_displayed_population() -> int:
	## Get the currently displayed population (may differ during animation)
	var result = 0
	for i in range(10):
		result = result * 10 + current_digits[i]
	return result
