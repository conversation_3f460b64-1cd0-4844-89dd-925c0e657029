extends Control

@onready var time_label: Label = %timeLabel
@onready var ampm_label: Label = %ampmLabel

@onready var location_options: VBoxContainer = %LocationOptions

var is_game_paused: bool = false

func _ready() -> void:
	# Connect to SignalBus signals for time updates and pause states
	SignalBus.time_changed.connect(_on_time_changed)
	SignalBus.game_paused.connect(_on_game_paused)
	SignalBus.game_resumed.connect(_on_game_resumed)

	# Initialize with current time from GameMechanics
	_update_time_display(GameMechanics.get_current_hours(), GameMechanics.get_current_minutes())


#region Time/Clock Functions
func _on_time_changed(hours: int, minutes: int):
	# Only update if game is not paused for efficiency
	if not is_game_paused:
		_update_time_display(hours, minutes)

func _on_game_paused():
	is_game_paused = true
	# Stop updating time display when paused

func _on_game_resumed():
	is_game_paused = false
	# Resume time updates and refresh display with current time
	_update_time_display(GameMechanics.get_current_hours(), GameMechanics.get_current_minutes())

func _update_time_display(hours: int, minutes: int):
	# Update time label with HH:MM format
	time_label.text = "%02d:%02d" % [hours, minutes]

	# Update AM/PM label based on 12-hour format
	if hours < 12:
		ampm_label.text = "AM"
	else:
		ampm_label.text = "PM"

#endregion

#region Location Options Functions
## Here we will generate x stacked buttons in the vbox location_options

## This will be based on the type of location we arrived at (fuel, food, people, other)

## Each button will clickable for now but later some will be 'locked' behind upgrades the player can buy later

## The button text should be short as possible with eseentially verb, count, and noun "Gather 18.2 Fuel" or "Collect 12 Food"

#endregion
