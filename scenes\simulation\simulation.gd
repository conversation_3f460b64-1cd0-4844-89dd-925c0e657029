extends Control

@onready var time_label: Label = %timeLabel
@onready var ampm_label: Label = %ampmLabel

@onready var location_options: VBoxContainer = %LocationOptions

var is_game_paused: bool = false

func _ready() -> void:
	# Connect to SignalBus signals for time updates and pause states
	SignalBus.time_changed.connect(_on_time_changed)
	SignalBus.game_paused.connect(_on_game_paused)
	SignalBus.game_resumed.connect(_on_game_resumed)

	# Connect to location interaction signals
	SignalBus.location_arrived.connect(_on_location_arrived)

	# Initialize with current time from GameMechanics
	_update_time_display(GameMechanics.get_current_hours(), GameMechanics.get_current_minutes())


#region Time/Clock Functions
func _on_time_changed(hours: int, minutes: int):
	# Only update if game is not paused for efficiency
	if not is_game_paused:
		_update_time_display(hours, minutes)

func _on_game_paused():
	is_game_paused = true
	# Stop updating time display when paused

func _on_game_resumed():
	is_game_paused = false
	# Resume time updates and refresh display with current time
	_update_time_display(GameMechanics.get_current_hours(), GameMechanics.get_current_minutes())

func _update_time_display(hours: int, minutes: int):
	# Update time label with HH:MM format
	time_label.text = "%02d:%02d" % [hours, minutes]

	# Update AM/PM label based on 12-hour format
	if hours < 12:
		ampm_label.text = "AM"
	else:
		ampm_label.text = "PM"

#endregion

#region Location Options Functions

# Location option definitions based on resource type
var location_options_data = {
	0: {  # FUEL locations (Wood icon)
		"common": [
			{"action": "Gather Wood", "resource": "fuel", "min": 8, "max": 15, "weight": 80},
			{"action": "Gather Berries", "resource": "food", "min": 3, "max": 8, "weight": 60},
			{"action": "Hunt Squirrels", "resource": "food", "min": 4, "max": 10, "weight": 40}
		],
		"uncommon": [
			{"action": "Salvage Fuel from Car", "resource": "fuel", "min": 15, "max": 25, "weight": 25},
			{"action": "Hunt Deer", "resource": "food", "min": 12, "max": 20, "weight": 20}
		]
	},
	1: {  # FOOD locations (Food icon)
		"common": [
			{"action": "Gather Berries", "resource": "food", "min": 5, "max": 12, "weight": 80},
			{"action": "Hunt Squirrels", "resource": "food", "min": 6, "max": 14, "weight": 60},
			{"action": "Raid Picnic", "resource": "food", "min": 10, "max": 18, "weight": 50}
		],
		"uncommon": [
			{"action": "Salvage Fuel from Car", "resource": "fuel", "min": 12, "max": 20, "weight": 30},
			{"action": "Hunt Deer", "resource": "food", "min": 15, "max": 25, "weight": 25},
			{"action": "Convert Humans", "resource": "followers", "min": 2, "max": 5, "weight": 15}
		]
	},
	2: {  # PEOPLE locations (Person icon)
		"common": [
			{"action": "Observe Humans", "resource": "food", "min": 2, "max": 6, "weight": 70},
			{"action": "Scavenge Supplies", "resource": "fuel", "min": 4, "max": 10, "weight": 60}
		],
		"uncommon": [
			{"action": "Convert Humans", "resource": "followers", "min": 3, "max": 8, "weight": 40},
			{"action": "Abduct Humans", "resource": "abducted", "min": 1, "max": 3, "weight": 25}
		]
	}
}

func _on_location_arrived(resource_type: int, _resource_node: Control):
	"""Called when player arrives at a location - generate and display options"""
	print("Location arrived! Resource type: ", resource_type)
	clear_location_options()
	add_location_header(resource_type)
	generate_location_options(resource_type)

func add_location_header(resource_type: int):
	"""Add a header label to show what type of location this is"""
	var header = Label.new()
	var location_names = {
		0: "⛽ Fuel Location",
		1: "🍎 Food Location",
		2: "👥 People Location",
		3: "🛸 Crop Circle"
	}
	header.text = location_names.get(resource_type, "📍 Unknown Location")
	header.add_theme_font_size_override("font_size", 16)
	header.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	location_options.add_child(header)

func clear_location_options():
	"""Remove all existing location option buttons"""
	for child in location_options.get_children():
		child.queue_free()
	SignalBus.emit_location_options_cleared()

func generate_location_options(resource_type: int):
	"""Generate 2-4 random options based on the resource type"""
	if not location_options_data.has(resource_type):
		print("No options defined for resource type: ", resource_type)
		return

	var options_data = location_options_data[resource_type]
	var selected_options = []

	# Always include at least one common option
	var common_options = options_data.get("common", [])
	var uncommon_options = options_data.get("uncommon", [])

	# Select 1-2 common options
	var num_common = randi_range(1, min(2, common_options.size()))
	for i in range(num_common):
		if common_options.size() > 0:
			var option = common_options[randi() % common_options.size()]
			if not selected_options.has(option):
				selected_options.append(option)

	# Maybe add an uncommon option (30% chance)
	if randf() < 0.3 and uncommon_options.size() > 0:
		var option = uncommon_options[randi() % uncommon_options.size()]
		if not selected_options.has(option):
			selected_options.append(option)

	# Create buttons for each selected option
	for option in selected_options:
		create_location_option_button(option)

func create_location_option_button(option_data: Dictionary):
	"""Create a button for a location option"""
	var button = Button.new()

	# Generate random amount within range
	var amount = randf_range(option_data.min, option_data.max)
	var formatted_amount = "%.1f" % amount if amount != int(amount) else str(int(amount))

	# Create button text
	var resource_name = get_resource_display_name(option_data.resource)
	button.text = "%s %s %s" % [option_data.action, formatted_amount, resource_name]

	# Style the button
	button.custom_minimum_size = Vector2(200, 40)

	# Store the option data and actual amount in the button
	button.set_meta("option_data", option_data)
	button.set_meta("amount", amount)

	# Connect button press
	button.pressed.connect(_on_location_option_pressed.bind(button))

	# Add to the container
	location_options.add_child(button)

	print("Created option: ", button.text)

func get_resource_display_name(resource_type: String) -> String:
	"""Get display name for resource types"""
	match resource_type:
		"fuel": return "Fuel"
		"food": return "Food"
		"followers": return "Followers"
		"abducted": return "Humans"
		_: return "Resources"

func _on_location_option_pressed(button: Button):
	"""Handle when a location option button is pressed"""
	var option_data = button.get_meta("option_data")
	var amount = button.get_meta("amount")

	print("Option selected: ", button.text)

	# Grant the resources to the player
	grant_resources(option_data.resource, amount)

	# Clear all location options (player can only choose one)
	clear_location_options()

	# Clear all resource nodes from the area scan
	clear_area_scan_nodes()

	# Emit signal for other systems
	SignalBus.emit_location_option_selected({"option": option_data, "amount": amount})

func grant_resources(resource_type: String, amount: float):
	"""Grant resources to the player based on the option selected"""
	match resource_type:
		"fuel":
			PlayerStats.current_fuel = min(PlayerStats.max_fuel_capacity, PlayerStats.current_fuel + amount)
			SignalBus.emit_player_fuel_changed(PlayerStats.current_fuel, PlayerStats.max_fuel_capacity)
			print("Granted %.1f fuel. Current: %.1f/%.1f" % [amount, PlayerStats.current_fuel, PlayerStats.max_fuel_capacity])

		"food":
			PlayerStats.add_food(amount)
			print("Granted %.1f food. Current: %.1f/%.1f" % [amount, PlayerStats.current_food, PlayerStats.max_food])

		"followers":
			var actual_followers = PlayerStats.add_followers(int(amount))
			print("Converted %d humans to followers. Total followers: %s" % [actual_followers, PlayerStats.format_population_number(PlayerStats.followers)])

		"abducted":
			# TODO: Implement abduction system when ship capacity is ready
			print("Abducted %.0f humans (not yet implemented)" % amount)

		_:
			print("Unknown resource type: ", resource_type)

func clear_area_scan_nodes():
	"""Tell the area scan to clear all resource nodes"""
	# Find the AreaScan node and call its clear function
	var main_scene = get_tree().current_scene
	var area_scan = main_scene.find_child("AreaScan", true, false)
	if area_scan and area_scan.has_method("clear_resource_nodes"):
		area_scan.clear_resource_nodes()
		print("Cleared all area scan nodes")

#endregion
