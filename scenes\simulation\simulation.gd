extends Control

@onready var time_label: Label = %timeLabel
@onready var ampm_label: Label = %ampmLabel

@onready var location_options: VBoxContainer = %LocationOptions

var is_game_paused: bool = false

func _ready() -> void:
	# Connect to SignalBus signals for time updates and pause states
	SignalBus.time_changed.connect(_on_time_changed)
	SignalBus.game_paused.connect(_on_game_paused)
	SignalBus.game_resumed.connect(_on_game_resumed)

	# Connect to location interaction signals
	SignalBus.location_arrived.connect(_on_location_arrived)

	# Initialize with current time from GameMechanics
	_update_time_display(GameMechanics.get_current_hours(), GameMechanics.get_current_minutes())


#region Time/Clock Functions
func _on_time_changed(hours: int, minutes: int):
	# Only update if game is not paused for efficiency
	if not is_game_paused:
		_update_time_display(hours, minutes)

func _on_game_paused():
	is_game_paused = true
	# Stop updating time display when paused

func _on_game_resumed():
	is_game_paused = false
	# Resume time updates and refresh display with current time
	_update_time_display(GameMechanics.get_current_hours(), GameMechanics.get_current_minutes())

func _update_time_display(hours: int, minutes: int):
	# Update time label with HH:MM format
	time_label.text = "%02d:%02d" % [hours, minutes]

	# Update AM/PM label based on 12-hour format
	if hours < 12:
		ampm_label.text = "AM"
	else:
		ampm_label.text = "PM"

#endregion

#region Location Options Functions

func _on_location_arrived(resource_type: int, _resource_node: Control):
	## Called when player arrives at a location - generate and display options
	print("Location arrived! Resource type: ", resource_type)
	clear_location_options()
	add_location_header(resource_type)
	generate_location_options(resource_type)

func add_location_header(resource_type: int):
	## Add a header label to show what type of location this is
	var header = Label.new()
	header.text = Locations.get_location_name(resource_type)
	header.add_theme_font_size_override("font_size", 16)
	header.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	location_options.add_child(header)

func clear_location_options():
	## Remove all existing location option buttons
	var child_count = location_options.get_child_count()
	if child_count > 0:
		print("Clearing ", child_count, " location options")
		for child in location_options.get_children():
			child.queue_free()
		SignalBus.emit_location_options_cleared()

func generate_location_options(resource_type: int):
	## Generate 2-4 random options based on the resource type
	var selected_options = Locations.generate_location_options(resource_type)

	# Create buttons for each selected option
	for option in selected_options:
		create_location_option_button(option)

func create_location_option_button(option_data: Dictionary):
	## Create a button for a location option
	var button = Button.new()

	# Generate random amount within range
	var amount = randf_range(option_data.min, option_data.max)

	# Create button text using Locations autoload
	button.text = Locations.create_option_button_text(option_data, amount)

	# Style the button
	button.custom_minimum_size = Vector2(200, 40)

	# Store the option data and actual amount in the button
	button.set_meta("option_data", option_data)
	button.set_meta("amount", amount)

	# Connect button press
	button.pressed.connect(_on_location_option_pressed.bind(button))

	# Add to the container
	location_options.add_child(button)

	print("Created option: ", button.text)

func _on_location_option_pressed(button: Button):
	## Handle when a location option button is pressed
	var option_data = button.get_meta("option_data")
	var amount = button.get_meta("amount")

	print("Option selected: ", button.text)

	# Grant the resources to the player
	grant_resources(option_data.resource, amount)

	# Clear all location options (player can only choose one)
	clear_location_options()

	# Clear all resource nodes from the area scan
	clear_area_scan_nodes()

	# Emit signal for other systems
	SignalBus.emit_location_option_selected({"option": option_data, "amount": amount})

func grant_resources(resource_type: String, amount: float):
	## Grant resources to the player based on the option selected
	Locations.grant_resources_to_player(resource_type, amount)

func clear_area_scan_nodes():
	## Tell the area scan to clear all resource nodes
	# Find the AreaScan node and call its clear function
	var main_scene = get_tree().current_scene
	var area_scan = main_scene.find_child("AreaScan", true, false)
	if area_scan and area_scan.has_method("clear_resource_nodes"):
		area_scan.clear_resource_nodes()
		print("Cleared all area scan nodes")

#endregion
