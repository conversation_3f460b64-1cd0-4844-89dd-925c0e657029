extends Control

@onready var streaming_text_panel: Panel = %StreamingTextPanel

## Ship's log system variables
var log_scroll_container: ScrollContainer = null
var log_vbox: VBoxContainer = null
var log_messages: Array[Label] = []
const MAX_LOG_MESSAGES = 20
const LOG_MESSAGE_HEIGHT = 30
const LOG_PADDING = 10

func _ready():
	setup_log_system()
	connect_signals()

func setup_log_system():
	## Create the scrolling log system inside the streaming text panel
	# Create scroll container
	log_scroll_container = ScrollContainer.new()
	log_scroll_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	log_scroll_container.add_theme_constant_override("margin_left", LOG_PADDING)
	log_scroll_container.add_theme_constant_override("margin_right", LOG_PADDING)
	log_scroll_container.add_theme_constant_override("margin_top", LOG_PADDING)
	log_scroll_container.add_theme_constant_override("margin_bottom", LOG_PADDING)
	streaming_text_panel.add_child(log_scroll_container)

	# Create VBox container for messages
	log_vbox = VBoxContainer.new()
	log_vbox.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	log_vbox.add_theme_constant_override("separation", 5)
	log_scroll_container.add_child(log_vbox)

	# Add initial welcome message
	add_log_message("Ship's Log System Online", Color.CYAN)
	add_log_message("Monitoring ship operations...", Color.LIGHT_GRAY)

func connect_signals():
	## Connect to relevant signals for logging
	SignalBus.crash_sequence_started.connect(_on_death_occurred)
	SignalBus.location_option_selected.connect(_on_resource_obtained)
	SignalBus.human_population_changed.connect(_on_population_changed)
	SignalBus.followers_changed.connect(_on_population_changed)
	SignalBus.followers_released.connect(_on_followers_released)

func add_log_message(text: String, color: Color = Color.WHITE):
	## Add a new message to the log system
	# Create new label for the message
	var message_label = Label.new()
	message_label.text = text
	message_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	message_label.vertical_alignment = VERTICAL_ALIGNMENT_TOP
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_LEFT
	message_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	message_label.custom_minimum_size.y = LOG_MESSAGE_HEIGHT
	message_label.add_theme_color_override("font_color", color)
	message_label.add_theme_font_size_override("font_size", 12)

	# Add to VBox and messages array
	log_vbox.add_child(message_label)
	log_messages.append(message_label)

	# Remove oldest message if we exceed the limit
	if log_messages.size() > MAX_LOG_MESSAGES:
		var oldest_message = log_messages[0]
		log_messages.remove_at(0)
		oldest_message.queue_free()

	# Auto-scroll to bottom to show newest message
	await get_tree().process_frame  # Wait for layout update
	log_scroll_container.scroll_vertical = int(log_scroll_container.get_v_scroll_bar().max_value)

## Signal handlers for logging events
func _on_death_occurred(death_cause: String):
	## Log death events
	var death_message = ""
	match death_cause:
		"food":
			death_message = "CRITICAL: Ship crew starved. Consciousness transfer initiated."
		"fuel":
			death_message = "CRITICAL: Ship fuel depleted. Emergency landing failed. Consciousness transfer initiated."
		_:
			death_message = "CRITICAL: Ship systems failed. Consciousness transfer initiated."

	add_log_message(death_message, Color.RED)

func _on_followers_released(count: int):
	## Log follower release events
	var message = "You showed mercy on %d humans. Your following grows." % count
	add_log_message(message, Color.CYAN)

func _on_resource_obtained(signal_data: Dictionary):
	## Log resource collection from locations
	# Signal format: {"option": option_data, "amount": amount}
	if signal_data.has("option") and signal_data.has("amount"):
		var option_data = signal_data.option
		var amount = signal_data.amount
		var action = option_data.get("action", "unknown action")
		var resource_type = option_data.get("resource", "unknown")

		var message = "Obtained %.1f %s from %s" % [amount, get_resource_display_name(resource_type), action.to_lower()]
		add_log_message(message, Color.LIGHT_GREEN)

func get_resource_display_name(resource_type: String) -> String:
	## Get display name for resource types
	match resource_type:
		"fuel": return "fuel"
		"food": return "food"
		"followers": return "followers"
		"abducted": return "humans"
		"research": return "research points"
		_: return "resources"

func _on_population_changed():
	## Log population and believer updates
	var population = PlayerStats.human_population
	var believers = PlayerStats.followers
	var percentage = 0.0

	if population > 0:
		percentage = (float(believers) / float(population)) * 100.0

	var message = "Population: %s  ||  Believers: %s [%.3f%%]" % [
		PlayerStats.format_population_number(population),
		PlayerStats.format_population_number(believers),
		percentage
	]
	add_log_message(message, Color.YELLOW)

func get_resource_name(resource_type: int) -> String:
	## Convert resource type enum to readable name
	match resource_type:
		0: return "fuel"
		1: return "food"
		2: return "people"
		3: return "crop circle data"
		_: return "unknown resource"
