extends Node

# Player Statistics - All upgradeable through currency
# Scan System
var scan_cooldown: float = 5.0 # seconds cooldown before player scan can be reactivated

# Health System
var max_health: float = 100.0
var current_health: float = 100.0
var max_food: float = 100.0
var current_food: float = 80.0
var food_consumption_rate: float = 0.5 # food per second

# Fuel System
var max_fuel_capacity: float = 100.0
var current_fuel: float = 100.0
var fuel_consumption_rate: float = 1.0 # fuel per second

# Movement System
var max_speed: float = 100.0 # maximum movement speed
var acceleration: float = 50.0 # how quickly we reach max speed
var ship_speed: float = 5.0 # pixels per second for moving to resources

# Resource System
var currency: int = 0 # for purchasing upgrades
var research_points: int = 0 # for unlocking new technologies

# Equipment System
var scanner_range: float = 500.0 # range of player scans
var scanner_accuracy: float = 0.8 # accuracy of scan results (0.0-1.0)

# Alien Invasion Game Mechanics
var human_population: int = 0 # Total human population on Earth
var followers: int = 0 # Humans who believe in aliens and want them to win
var ship_human_capacity: int = 5 # Maximum humans that can be held on ship (upgradeable)

func _ready():
	print("PlayerStats._ready() called - current_food: ", current_food)
	# Only initialize with default values if no save file exists
	# SaveLoad runs before PlayerStats in autoload order, so save should already be loaded
	if not FileAccess.file_exists("user://savegame.json"):
		print("No save file found, initializing PlayerStats with defaults")
		reset_to_defaults()
	else:
		print("Save file exists, PlayerStats should already be loaded by SaveLoad")
		print("Current food after SaveLoad should have loaded: ", current_food, "/", max_food)

func _process(delta: float):
	# Handle food consumption over time (only when game is not paused)
	if not GameMechanics.is_paused:
		consume_food_over_time(delta)

func reset_to_defaults():
	# Reset all stats to default values (useful for new games)
	print("PlayerStats: Resetting to defaults")
	scan_cooldown = 5.0
	max_health = 100.0
	current_health = max_health
	max_food = 100.0
	current_food = 80.0
	food_consumption_rate = 0.5
	print("PlayerStats: Reset food to ", current_food, "/", max_food)
	# Emit signal so UI updates
	SignalBus.emit_player_food_changed(current_food, max_food)
	max_fuel_capacity = 1000.0
	current_fuel = max_fuel_capacity
	fuel_consumption_rate = 1.0
	max_speed = 100.0
	acceleration = 50.0
	ship_speed = 5.0
	currency = 0
	research_points = 0
	scanner_range = 500.0
	scanner_accuracy = 0.8

	# Initialize alien invasion game mechanics with randomized human population
	generate_initial_human_population()
	followers = 0
	ship_human_capacity = 5
	print("PlayerStats: Reset food to ", current_food, "/", max_food)
	print("PlayerStats: Initialized invasion - Population: ", format_population_number(human_population), " Followers: ", followers, " Ship Capacity: ", ship_human_capacity)
	# Emit signals so UI updates
	SignalBus.emit_player_food_changed(current_food, max_food)

# Utility functions for common operations
func take_damage(amount: float):
	current_health = max(0, current_health - amount)
	SignalBus.emit_player_health_changed(current_health, max_health)

func heal(amount: float):
	current_health = min(max_health, current_health + amount)
	SignalBus.emit_player_health_changed(current_health, max_health)

func consume_fuel(amount: float):
	current_fuel = max(0, current_fuel - amount)
	SignalBus.emit_player_fuel_changed(current_fuel, max_fuel_capacity)

func add_fuel(amount: float):
	current_fuel = min(max_fuel_capacity, current_fuel + amount)
	SignalBus.emit_player_fuel_changed(current_fuel, max_fuel_capacity)

func add_currency(amount: int):
	currency += amount
	SignalBus.emit_player_currency_changed(currency)

func spend_currency(amount: int) -> bool:
	if currency >= amount:
		currency -= amount
		SignalBus.emit_player_currency_changed(currency)
		return true
	return false

func add_research_points(amount: int):
	research_points += amount
	SignalBus.emit_player_research_changed(research_points)

# Food management functions
func consume_food_over_time(delta: float):
	"""Consume food over time based on consumption rate"""
	if current_food > 0:
		var food_consumed = food_consumption_rate * delta
		current_food = max(0, current_food - food_consumed)
		SignalBus.emit_player_food_changed(current_food, max_food)

		# If food reaches zero, start taking health damage
		if current_food <= 0:
			take_damage(1.0 * delta)  # 1 health per second when starving

func consume_food(amount: float):
	"""Manually consume food (for specific actions)"""
	current_food = max(0, current_food - amount)
	SignalBus.emit_player_food_changed(current_food, max_food)

func add_food(amount: float):
	"""Add food to current supply"""
	current_food = min(max_food, current_food + amount)
	SignalBus.emit_player_food_changed(current_food, max_food)

# Alien Invasion Game Mechanics Functions
func generate_initial_human_population():
	"""Generate a randomized human population around 8.2 billion"""
	# Base population of 8.2 billion with +/- 100 million variance
	var base_population = **********
	var variance = randi_range(-100000000, 100000000)
	human_population = base_population + variance
	print("Generated initial human population: ", format_population_number(human_population))

func format_population_number(population: int) -> String:
	"""Format large population numbers for display (e.g., 8.2B)"""
	if population >= **********:
		return "%.1fB" % (population / **********.0)
	elif population >= 1000000:
		return "%.1fM" % (population / 1000000.0)
	elif population >= 1000:
		return "%.1fK" % (population / 1000.0)
	else:
		return str(population)

func check_victory_condition() -> bool:
	"""Check if the game is won (human population equals followers)"""
	return human_population == followers

func get_remaining_unconverted_humans() -> int:
	"""Get the number of humans that are neither followers nor dead"""
	return human_population - followers

func reduce_human_population(amount: int):
	"""Reduce human population (through destruction/death)"""
	human_population = max(0, human_population - amount)
	SignalBus.emit_human_population_changed(human_population)
	check_and_emit_victory()

func add_followers(amount: int):
	"""Convert humans to followers (cannot exceed current population)"""
	var max_new_followers = human_population - followers
	var actual_new_followers = min(amount, max_new_followers)
	followers += actual_new_followers
	SignalBus.emit_followers_changed(followers)
	check_and_emit_victory()
	return actual_new_followers

func remove_followers(amount: int):
	"""Remove followers (they become regular humans again)"""
	followers = max(0, followers - amount)
	SignalBus.emit_followers_changed(followers)

func upgrade_ship_capacity(new_capacity: int):
	"""Upgrade the ship's human carrying capacity"""
	ship_human_capacity = new_capacity
	SignalBus.emit_ship_human_capacity_changed(ship_human_capacity)

func check_and_emit_victory():
	"""Check victory condition and emit signal if met"""
	if check_victory_condition():
		SignalBus.emit_victory_condition_met(human_population, followers)
		print("VICTORY! Human population (", format_population_number(human_population), ") equals followers (", format_population_number(followers), ")")
