extends Node

# Player Statistics - All upgradeable through currency
# Scan System
var scan_cooldown: float = 5.0 # seconds cooldown before player scan can be reactivated

# Health System
var max_health: float = 100.0
var current_health: float = 100.0
var max_food: float = 100.0
var current_food: float = 80.0
var food_daily_consumption: float = 10.0 # food consumed per game day

# Fuel System
var max_fuel_capacity: float = 1000.0
var current_fuel: float = 100.0
var fuel_consumption_rate: float = 5.0 # fuel per second

# Movement System
var max_speed: float = 100.0 # maximum movement speed
var acceleration: float = 50.0 # how quickly we reach max speed
var ship_speed: float = 5.0 # pixels per second for moving to resources

# Resource System
var currency: int = 0 # for purchasing upgrades
var research_points: int = 0 # for unlocking new technologies

# Equipment System
var scanner_range: float = 500.0 # range of player scans
var scanner_accuracy: float = 0.8 # accuracy of scan results (0.0-1.0)

# Alien Invasion Game Mechanics
var human_population: int = 0 # Total human population on Earth
var followers: int = 0 # Humans who believe in aliens and want them to win
var ship_human_capacity: int = 5 # Maximum humans that can be held on ship (upgradeable)

# Human Resource Values
var human_food_value: int = 20 # Food points gained from consuming a human
var human_fuel_value: int = 50 # Fuel points gained from processing a human
var human_research_value: int = 1 # Research points gained from dissecting a human

# Human Processing Times (in seconds)
var human_eat_time: float = 10.0 # Time to process human for food
var human_fuel_time: float = 20.0 # Time to process human for fuel
var human_disect_time: float = 30.0 # Time to dissect human for research
var human_probe_time: float = 10.0 # Time to probe/convert human to follower

# Current Cargo
var current_cargo_humans: int = 0 # Current humans in cargo
var current_cargo_followers: int = 0 # Current followers in cargo (subset of humans)

# Death Tracking (for achievements and statistics)
var deaths_food: int = 0 # Deaths caused by starvation
var deaths_fuel: int = 0 # Deaths caused by fuel depletion
var deaths_total: int = 0 # Total deaths across all causes

func _ready():
	print("PlayerStats._ready() called - current_food: ", current_food)
	print("PlayerStats._ready() called - food_daily_consumption: ", food_daily_consumption)
	# Only initialize with default values if no save file exists
	# SaveLoad runs before PlayerStats in autoload order, so save should already be loaded
	if not FileAccess.file_exists("user://savegame.json"):
		print("No save file found, initializing PlayerStats with defaults")
		reset_to_defaults()
	else:
		print("Save file exists, PlayerStats should already be loaded by SaveLoad")
		print("Current food after SaveLoad should have loaded: ", current_food, "/", max_food)
		print("food_daily_consumption after SaveLoad: ", food_daily_consumption)

func _process(delta: float):
	# Handle food consumption over time (only when game is not paused)
	if not GameMechanics.is_paused:
		consume_food_over_time(delta)

func reset_to_defaults():
	# Reset all stats to default values (useful for new games)
	print("PlayerStats: Resetting to defaults")
	scan_cooldown = 5.0
	max_health = 100.0
	current_health = max_health
	max_food = 100.0
	current_food = 80.0
	food_daily_consumption = 10.0
	print("PlayerStats: Reset food to ", current_food, "/", max_food)
	# Emit signal so UI updates
	SignalBus.emit_player_food_changed(current_food, max_food)
	max_fuel_capacity = 1000.0
	current_fuel = max_fuel_capacity
	fuel_consumption_rate = 5.0
	max_speed = 100.0
	acceleration = 50.0
	ship_speed = 5.0
	currency = 0
	research_points = 0
	scanner_range = 500.0
	scanner_accuracy = 0.8

	# Initialize alien invasion game mechanics with randomized human population
	generate_initial_human_population()
	followers = 0
	ship_human_capacity = 5

	# Initialize human resource values
	human_food_value = 20
	human_fuel_value = 50
	human_research_value = 1

	# Initialize cargo
	current_cargo_humans = 0
	current_cargo_followers = 0
	print("PlayerStats: Reset food to ", current_food, "/", max_food)
	print("PlayerStats: Initialized invasion - Population: ", format_population_number(human_population), " Followers: ", followers, " Ship Capacity: ", ship_human_capacity)
	# Emit signals so UI updates
	SignalBus.emit_player_food_changed(current_food, max_food)

# Utility functions for common operations
func take_damage(amount: float):
	current_health = max(0, current_health - amount)
	SignalBus.emit_player_health_changed(current_health, max_health)

func heal(amount: float):
	current_health = min(max_health, current_health + amount)
	SignalBus.emit_player_health_changed(current_health, max_health)

func consume_fuel(amount: float):
	current_fuel = max(0, current_fuel - amount)
	SignalBus.emit_player_fuel_changed(current_fuel, max_fuel_capacity)

func add_fuel(amount: float):
	## Add fuel gradually over 3 seconds for satisfying "filling" effect
	add_resource_gradually("fuel", amount, 3.0)

func add_fuel_instantly(amount: float):
	## Add fuel immediately (for internal use or special cases)
	current_fuel = min(max_fuel_capacity, current_fuel + amount)
	SignalBus.emit_player_fuel_changed(current_fuel, max_fuel_capacity)

func add_currency(amount: int):
	currency += amount
	SignalBus.emit_player_currency_changed(currency)

func spend_currency(amount: int) -> bool:
	if currency >= amount:
		currency -= amount
		SignalBus.emit_player_currency_changed(currency)
		return true
	return false

func add_research_points(amount: int):
	research_points += amount
	SignalBus.emit_player_research_changed(research_points)

func handle_death(death_cause: String):
	## Handle player death and reset ship resources while preserving world progress
	print("Player death detected: ", death_cause)

	# Increment death counters
	match death_cause:
		"food":
			deaths_food += 1
		"fuel":
			deaths_fuel += 1
	deaths_total += 1

	print("Death stats - Food: %d, Fuel: %d, Total: %d" % [deaths_food, deaths_fuel, deaths_total])

	# Handle cargo loss - all humans in cargo are lost in the crash
	if current_cargo_humans > 0:
		print("Cargo lost in crash: %d humans (%d followers)" % [current_cargo_humans, current_cargo_followers])
		# Remove lost humans from population
		reduce_human_population(current_cargo_humans)
		# Remove lost followers from follower count
		if current_cargo_followers > 0:
			remove_followers(current_cargo_followers)
		# Clear cargo
		current_cargo_humans = 0
		current_cargo_followers = 0
		# Emit cargo update signal
		SignalBus.emit_cargo_changed(current_cargo_humans, current_cargo_followers)

	# Reset ship resources to full (ship gets replaced by mothership)
	current_food = max_food
	current_fuel = max_fuel_capacity
	current_health = max_health

	# Emit signals to update UI
	SignalBus.emit_player_food_changed(current_food, max_food)
	SignalBus.emit_player_fuel_changed(current_fuel, max_fuel_capacity)
	SignalBus.emit_player_health_changed(current_health, max_health)

	# World progress (human_population, followers, research_points, currency) remains unchanged
	print("Ship resources reset - Food: %.1f/%.1f, Fuel: %.1f/%.1f, Health: %.1f/%.1f" % [
		current_food, max_food, current_fuel, max_fuel_capacity, current_health, max_health
	])

	# Save the game to persist death count and reset resources
	SaveLoad.save_game()
	print("Game saved after death")

# Food management functions
func consume_food_over_time(delta: float):
	## Consume food over time based on daily consumption rate scaled to game time speed
	if current_food > 0:
		# Calculate food consumption per real second based on daily consumption and game speed
		# 1 game day = 1440 game minutes
		# GameMechanics.game_minutes_per_real_second determines how fast time passes
		var game_day_duration_in_real_seconds = 1440.0 / GameMechanics.game_minutes_per_real_second
		var food_per_real_second = food_daily_consumption / game_day_duration_in_real_seconds
		var food_consumed = food_per_real_second * delta

		# Debug output to track consumption
		#if randf() < 0.005:  # Print occasionally (0.5% chance per frame)
			#print("=== FOOD CONSUMPTION DEBUG ===")
			#print("food_daily_consumption: ", food_daily_consumption)
			#print("game_minutes_per_real_second: ", GameMechanics.game_minutes_per_real_second)
			#print("game_day_duration_in_real_seconds: ", game_day_duration_in_real_seconds)
			#print("food_per_real_second: ", food_per_real_second)
			#print("delta: ", delta)
			#print("food_consumed this frame: ", food_consumed)
			#print("current_food before: ", current_food)
			#print("Expected consumption rate: 10 food / ", game_day_duration_in_real_seconds, " seconds = ", 10.0 / game_day_duration_in_real_seconds, " food/sec")
			#print("==============================")

		current_food = max(0, current_food - food_consumed)
		SignalBus.emit_player_food_changed(current_food, max_food)

		# If food reaches zero, start taking health damage
		if current_food <= 0:
			take_damage(1.0 * delta)  # 1 health per second when starving

func consume_food(amount: float):
	## Manually consume food (for specific actions)
	current_food = max(0, current_food - amount)
	SignalBus.emit_player_food_changed(current_food, max_food)

func add_food(amount: float):
	## Add food gradually over 3 seconds for satisfying "filling" effect
	add_resource_gradually("food", amount, 3.0)

func add_food_instantly(amount: float):
	## Add food immediately (for internal use or special cases)
	current_food = min(max_food, current_food + amount)
	SignalBus.emit_player_food_changed(current_food, max_food)

## Gradual Resource Filling System
var resource_tweens: Dictionary = {}  # Track active resource tweens

func add_resource_gradually(resource_type: String, amount: float, duration: float):
	## Add resources gradually over specified duration for satisfying "filling" effect

	# Stop any existing tween for this resource type
	if resource_tweens.has(resource_type) and resource_tweens[resource_type]:
		resource_tweens[resource_type].kill()

	# Store starting values
	var start_value: float
	var max_value: float

	match resource_type:
		"food":
			start_value = current_food
			max_value = max_food
		"fuel":
			start_value = current_fuel
			max_value = max_fuel_capacity
		_:
			print("Error: Unknown resource type for gradual filling: ", resource_type)
			return

	# Calculate target value (clamped to maximum)
	var target_value = min(max_value, start_value + amount)
	var actual_amount = target_value - start_value

	# If no actual change needed, return early
	if actual_amount <= 0:
		print("Resource already at maximum, no gradual filling needed")
		return

	# Create and configure tween
	var tween = create_tween()
	resource_tweens[resource_type] = tween

	# Create update function for the tween
	var update_func = func(value: float):
		match resource_type:
			"food":
				current_food = value
				SignalBus.emit_player_food_changed(current_food, max_food)
			"fuel":
				current_fuel = value
				SignalBus.emit_player_fuel_changed(current_fuel, max_fuel_capacity)

	# Animate the resource value
	tween.tween_method(update_func, start_value, target_value, duration).set_ease(Tween.EASE_OUT)

	# Clean up tween reference when complete
	tween.tween_callback(func():
		resource_tweens.erase(resource_type)
		print("Gradual %s filling complete: %.1f added over %.1f seconds" % [resource_type, actual_amount, duration])
	)

	print("Started gradual %s filling: %.1f over %.1f seconds (%.1f -> %.1f)" % [
		resource_type, actual_amount, duration, start_value, target_value
	])

# Alien Invasion Game Mechanics Functions
func generate_initial_human_population():
	## Generate a randomized human population around 8.2 billion
	# Base population of 8.2 billion with +/- 100 million variance
	var base_population = 8200000000
	var variance = randi_range(-100000000, 100000000)
	human_population = base_population + variance
	print("Generated initial human population: ", format_population_number(human_population))

func format_population_number(population: int) -> String:
	## Format large population numbers for display (e.g., 8.2B)
	if population >= 1000000000:
		return "%.1fB" % (population / 1000000000.0)
	elif population >= 1000000:
		return "%.1fM" % (population / 1000000.0)
	elif population >= 1000:
		return "%.1fK" % (population / 1000.0)
	else:
		return str(population)

func check_victory_condition() -> bool:
	## Check if the game is won (human population equals followers)
	return human_population == followers

func get_remaining_unconverted_humans() -> int:
	## Get the number of humans that are neither followers nor dead
	return human_population - followers

func reduce_human_population(amount: int):
	## Reduce human population (through destruction/death)
	human_population = max(0, human_population - amount)
	SignalBus.emit_human_population_changed(human_population)
	check_and_emit_victory()

func add_followers(amount: int):
	## Convert humans to followers (cannot exceed current population)
	var max_new_followers = human_population - followers
	var actual_new_followers = min(amount, max_new_followers)
	followers += actual_new_followers
	SignalBus.emit_followers_changed(followers)
	check_and_emit_victory()
	return actual_new_followers

func remove_followers(amount: int):
	## Remove followers (they become regular humans again)
	followers = max(0, followers - amount)
	SignalBus.emit_followers_changed(followers)

func upgrade_ship_capacity(new_capacity: int):
	## Upgrade the ship's human carrying capacity
	ship_human_capacity = new_capacity
	SignalBus.emit_ship_human_capacity_changed(ship_human_capacity)

func check_and_emit_victory():
	## Check victory condition and emit signal if met
	if check_victory_condition():
		SignalBus.emit_victory_condition_met(human_population, followers)
		print("VICTORY! Human population (", format_population_number(human_population), ") equals followers (", format_population_number(followers), ")")

# Cargo Management Functions
func add_humans_to_cargo(amount: int) -> int:
	## Add humans to cargo, respecting capacity limits
	var available_space = ship_human_capacity - current_cargo_humans
	var actual_amount = min(amount, available_space)
	current_cargo_humans += actual_amount
	SignalBus.emit_cargo_changed(current_cargo_humans, current_cargo_followers)
	print("Added %d humans to cargo. Current: %d/%d" % [actual_amount, current_cargo_humans, ship_human_capacity])
	return actual_amount

func get_cargo_space_available() -> int:
	## Get remaining cargo space
	return ship_human_capacity - current_cargo_humans

func consume_human_for_food() -> bool:
	## Consume a human from cargo for food (prioritize non-followers)
	if current_cargo_humans <= 0:
		return false

	var is_follower = false
	# Prioritize non-followers first
	if current_cargo_humans > current_cargo_followers:
		# We have non-followers available
		is_follower = false
	else:
		# Only followers left
		is_follower = true
		current_cargo_followers -= 1
		remove_followers(1)

	# Remove human from cargo and population
	current_cargo_humans -= 1
	reduce_human_population(1)

	# Add food
	add_food_instantly(human_food_value)

	# Emit signals
	SignalBus.emit_cargo_changed(current_cargo_humans, current_cargo_followers)

	print("Consumed human for food: +%d food (was follower: %s)" % [human_food_value, is_follower])
	return true

func consume_human_for_fuel() -> bool:
	## Consume a human from cargo for fuel (prioritize non-followers)
	if current_cargo_humans <= 0:
		return false

	var is_follower = false
	# Prioritize non-followers first
	if current_cargo_humans > current_cargo_followers:
		# We have non-followers available
		is_follower = false
	else:
		# Only followers left
		is_follower = true
		current_cargo_followers -= 1
		remove_followers(1)

	# Remove human from cargo and population
	current_cargo_humans -= 1
	reduce_human_population(1)

	# Add fuel
	add_fuel_instantly(human_fuel_value)

	# Emit signals
	SignalBus.emit_cargo_changed(current_cargo_humans, current_cargo_followers)

	print("Consumed human for fuel: +%d fuel (was follower: %s)" % [human_fuel_value, is_follower])
	return true

func consume_human_for_research() -> bool:
	## Consume a human from cargo for research (prioritize non-followers)
	if current_cargo_humans <= 0:
		return false

	var is_follower = false
	# Prioritize non-followers first
	if current_cargo_humans > current_cargo_followers:
		# We have non-followers available
		is_follower = false
	else:
		# Only followers left
		is_follower = true
		current_cargo_followers -= 1
		remove_followers(1)

	# Remove human from cargo and population
	current_cargo_humans -= 1
	reduce_human_population(1)

	# Add research points
	add_research_points(human_research_value)

	# Emit signals
	SignalBus.emit_cargo_changed(current_cargo_humans, current_cargo_followers)

	print("Dissected human for research: +%d research (was follower: %s)" % [human_research_value, is_follower])
	return true

func convert_human_to_follower() -> bool:
	## Convert a non-follower human in cargo to a follower
	var non_followers_in_cargo = current_cargo_humans - current_cargo_followers
	if non_followers_in_cargo <= 0:
		return false

	# Convert one human to follower
	current_cargo_followers += 1
	add_followers(1)

	# Emit signals
	SignalBus.emit_cargo_changed(current_cargo_humans, current_cargo_followers)

	print("Converted human to follower. Cargo followers: %d/%d" % [current_cargo_followers, current_cargo_humans])
	return true

func get_non_followers_in_cargo() -> int:
	## Get number of non-followers in cargo
	return current_cargo_humans - current_cargo_followers

func release_followers_at_location():
	## Release all followers from cargo when arriving at a location
	if current_cargo_followers > 0:
		var released_count = current_cargo_followers
		print("Released %d followers at location" % released_count)

		current_cargo_humans -= current_cargo_followers
		current_cargo_followers = 0
		SignalBus.emit_cargo_changed(current_cargo_humans, current_cargo_followers)

		# Emit signal for computer screen logging
		SignalBus.emit_followers_released(released_count)
