extends Control

@onready var debug_button: Button = $Button
@onready var debug_panel: Panel = $DebugPanel
@onready var debug_grid: GridContainer = %debugGrid

func _ready():
	## Initialize debug console
	# Hide debug panel initially and disable mouse interaction
	hide_debug_panel()

	# Connect debug button to toggle panel
	debug_button.pressed.connect(_on_debug_button_pressed)

	# Configure grid layout (3 columns for organized button layout)
	debug_grid.columns = 3

	# Create debug buttons
	create_debug_buttons()

func _on_debug_button_pressed():
	## Toggle debug panel visibility
	if debug_panel.visible:
		hide_debug_panel()
	else:
		show_debug_panel()

func hide_debug_panel():
	## Properly hide debug panel so it doesn't interfere with other UI
	debug_panel.visible = false
	debug_panel.mouse_filter = Control.MOUSE_FILTER_IGNORE
	# Move panel off-screen as extra safety
	debug_panel.position = Vector2(-2000, -2000)

func show_debug_panel():
	## Show debug panel and enable mouse interaction
	debug_panel.visible = true
	debug_panel.mouse_filter = Control.MOUSE_FILTER_PASS
	# Move panel back to original position
	debug_panel.position = Vector2(42, 97)

func create_debug_buttons():
	## Create all debug buttons for resource manipulation
	# Food debug buttons
	create_debug_button("Add 10 Food", _on_add_10_food)
	create_debug_button("Fill Food", _on_fill_food)
	create_debug_button("Empty Food", _on_empty_food)

	# Fuel debug buttons
	create_debug_button("Add 10 Fuel", _on_add_10_fuel)
	create_debug_button("Fill Fuel", _on_fill_fuel)
	create_debug_button("Empty Fuel", _on_empty_fuel)

	# Health debug buttons
	create_debug_button("Add 10 Health", _on_add_10_health)
	create_debug_button("Fill Health", _on_fill_health)
	create_debug_button("Damage 10 Health", _on_damage_10_health)

	# Currency debug buttons
	create_debug_button("Add 100 Currency", _on_add_100_currency)
	create_debug_button("Add 1000 Currency", _on_add_1000_currency)

	# Research debug buttons
	create_debug_button("Add 50 Research", _on_add_50_research)
	create_debug_button("Add 500 Research", _on_add_500_research)

	# Developer debug buttons (flexible for custom fixes)
	create_debug_button("Dev 1", _on_dev_1)
	create_debug_button("Dev 2", _on_dev_2)
	create_debug_button("Dev 3", _on_dev_3)
	create_debug_button("Dev 4", _on_dev_4)
	create_debug_button("Dev 5", _on_dev_5)

func create_debug_button(text: String, callback: Callable):
	## Helper function to create a debug button with callback
	var button = Button.new()
	button.text = text
	button.pressed.connect(callback)
	debug_grid.add_child(button)

## Food Debug Functions
func _on_add_10_food():
	PlayerStats.add_food(10.0)
	print("Debug: Added 10 food. Current: %.1f/%.1f" % [PlayerStats.current_food, PlayerStats.max_food])

func _on_fill_food():
	PlayerStats.current_food = PlayerStats.max_food
	SignalBus.emit_player_food_changed(PlayerStats.current_food, PlayerStats.max_food)
	print("Debug: Filled food to max. Current: %.1f/%.1f" % [PlayerStats.current_food, PlayerStats.max_food])

func _on_empty_food():
	PlayerStats.current_food = 0.0
	SignalBus.emit_player_food_changed(PlayerStats.current_food, PlayerStats.max_food)
	print("Debug: Emptied food. Current: %.1f/%.1f" % [PlayerStats.current_food, PlayerStats.max_food])

## Fuel Debug Functions
func _on_add_10_fuel():
	PlayerStats.add_fuel(10.0)
	print("Debug: Added 10 fuel. Current: %.1f/%.1f" % [PlayerStats.current_fuel, PlayerStats.max_fuel_capacity])

func _on_fill_fuel():
	PlayerStats.current_fuel = PlayerStats.max_fuel_capacity
	SignalBus.emit_player_fuel_changed(PlayerStats.current_fuel, PlayerStats.max_fuel_capacity)
	print("Debug: Filled fuel to max. Current: %.1f/%.1f" % [PlayerStats.current_fuel, PlayerStats.max_fuel_capacity])

func _on_empty_fuel():
	PlayerStats.current_fuel = 0.0
	SignalBus.emit_player_fuel_changed(PlayerStats.current_fuel, PlayerStats.max_fuel_capacity)
	print("Debug: Emptied fuel. Current: %.1f/%.1f" % [PlayerStats.current_fuel, PlayerStats.max_fuel_capacity])

## Health Debug Functions
func _on_add_10_health():
	PlayerStats.heal(10.0)
	print("Debug: Added 10 health. Current: %.1f/%.1f" % [PlayerStats.current_health, PlayerStats.max_health])

func _on_fill_health():
	PlayerStats.current_health = PlayerStats.max_health
	SignalBus.emit_player_health_changed(PlayerStats.current_health, PlayerStats.max_health)
	print("Debug: Filled health to max. Current: %.1f/%.1f" % [PlayerStats.current_health, PlayerStats.max_health])

func _on_damage_10_health():
	PlayerStats.take_damage(10.0)
	print("Debug: Took 10 damage. Current: %.1f/%.1f" % [PlayerStats.current_health, PlayerStats.max_health])

## Currency Debug Functions
func _on_add_100_currency():
	PlayerStats.add_currency(100)
	print("Debug: Added 100 currency. Current: %d" % PlayerStats.currency)

func _on_add_1000_currency():
	PlayerStats.add_currency(1000)
	print("Debug: Added 1000 currency. Current: %d" % PlayerStats.currency)

## Research Debug Functions
func _on_add_50_research():
	PlayerStats.add_research_points(50)
	print("Debug: Added 50 research points. Current: %d" % PlayerStats.research_points)

func _on_add_500_research():
	PlayerStats.add_research_points(500)
	print("Debug: Added 500 research points. Current: %d" % PlayerStats.research_points)

## Developer Debug Functions (flexible for custom fixes)
func _on_dev_1():
	## Dev 1: Fix a save file issue
	PlayerStats.fuel_consumption_rate = 5.0
	PlayerStats.max_fuel_capacity = 1000.0
	#print("Current food_daily_consumption: ", PlayerStats.food_daily_consumption)


func _on_dev_2():
	## Dev 2: Custom debug function (modify as needed)
	print("Dev 2: Custom debug function called")
	print("Current PlayerStats values:")
	print("  food_daily_consumption: ", PlayerStats.food_daily_consumption)
	print("  current_food: ", PlayerStats.current_food)
	print("  max_food: ", PlayerStats.max_food)
	print("  game_minutes_per_real_second: ", GameMechanics.game_minutes_per_real_second)

func _on_dev_3():
	## Dev 3: Placeholder function (customize as needed)
	#print("Dev 3: Placeholder function called - ready for customization")
	PlayerStats.consume_food(5)
	pass

func _on_dev_4():
	## Dev 4: Placeholder function (customize as needed)
	print("Dev 4: Placeholder function called - ready for customization")
	pass

func _on_dev_5():
	## Dev 5: Placeholder function (customize as needed)
	print("Dev 5: Placeholder function called - ready for customization")
	pass
